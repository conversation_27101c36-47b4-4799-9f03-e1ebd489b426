package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.SysUserPostVo;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.ISysPostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 岗位信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/post")
public class SysPostController extends BaseController
{
    @Autowired
    private ISysPostService postService;

    @Autowired
    private ISysOperLogService sysOperLogService;
    /**
     * 获取岗位列表
     */
//    @PreAuthorize("@ss.hasPermi('system:postNew:view')")
    @GetMapping("/list")
    public TableDataInfo list(SysPost post)
    {
        startPage();
        List<SysPost> list = postService.selectPostList(post);
        TableDataInfo tdi= getDataTable(list);


        for (SysPost sysPost : list) {
        	sysPost.setDeptName(getDeptBreadcrumb(sysPost.getDept()));
		}
        return tdi;
    }

    /**
     * 不包含权限标识，过滤多余信息的岗位列表接口
     * @param post
     * @return
     */
    @GetMapping("/noPermissionList")
    public TableDataInfo noPermissionList(SysPost post)
    {
        startPage();
        List<SysPost> list = postService.selectNoPermissionPostList(post);
        TableDataInfo tdi= getDataTable(list);


        for (SysPost sysPost : list) {
            sysPost.setDeptName(getDeptBreadcrumb(sysPost.getDept()));
        }
        return tdi;
    }

    @PreAuthorize("@ss.hasPermi('system:post:export')")
    @Log(title = "岗位管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPost post)
    {
        List<SysPost> list = postService.selectPostList(post);
        ExcelUtil<SysPost> util = new ExcelUtil<SysPost>(SysPost.class);
        util.exportExcel(response, list, "岗位数据");
    }

    /**
     * 根据岗位编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:postNew:view')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable Long postId)
    {
    	SysPost sp=postService.selectPostById(postId);
    	sp.setDeptName(getDeptBreadcrumb(sp.getDept()));
        return AjaxResult.success(sp);
    }

    /**
     * 新增岗位
     */
    @PreAuthorize("@ss.hasPermi('system:postNew:add')")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 3000)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysPost post)
    {
        String errorMsg = "";
        String operMessage = getUsername() + "新增了【" + post.getPostName() + "】岗位";
        if (UserConstants.NOT_UNIQUE.equals(postService.checkPostNameUnique(post)))
        {
            errorMsg = "新增岗位【" + post.getPostName() + "】失败，该公司下岗位名称已存在";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMessage, 1, errorMsg,"");
            return AjaxResult.error(errorMsg);
        }
        else if (UserConstants.NOT_UNIQUE.equals(postService.checkPostCodeUnique(post)))
        {
            errorMsg = "新增岗位【" + post.getPostName() + "】失败，岗位编码已存在";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMessage, 1, errorMsg,"");
            return AjaxResult.error(errorMsg);
        }
        post.setCreateBy(getUsername());
        return toAjax(postService.insertPost(post));
    }

    /**
     * 修改岗位
     */
    @PreAuthorize("@ss.hasPermi('system:postNew:edit')")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit(interval = 3000)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysPost post)
    {
        String errorMsg = "";
        String operMessage = getUsername() + "修改了【" + post.getPostName() + "】岗位";
        if (UserConstants.NOT_UNIQUE.equals(postService.checkPostNameUnique(post)))
        {
            errorMsg = "修改岗位【" + post.getPostName() + "】失败，该公司下岗位名称已存在";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMessage, 2, errorMsg,"");
            return AjaxResult.error(errorMsg);
        }
        else if (UserConstants.NOT_UNIQUE.equals(postService.checkPostCodeUnique(post)))
        {
            errorMsg = "修改岗位【" + post.getPostName() + "】失败，岗位编码已存在";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMessage, 2, errorMsg,"");
            return AjaxResult.error(errorMsg);
        }
        else if (post.getPostId()==post.getLeader())
        {
            errorMsg = "修改岗位【" + post.getPostName() + "】失败，岗位负责人不能选择本岗位";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(),operMessage, 2, errorMsg,"");
            return AjaxResult.error(errorMsg);
        }
        post.setUpdateBy(getUsername());
        return toAjax(postService.updatePost(post));
    }

    /**
     * 删除岗位
     */
    @PreAuthorize("@ss.hasPermi('system:postNew:remove')")
    @Log(title = "岗位管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    public AjaxResult remove(@PathVariable Long[] postIds)
    {
        for (Long postId : postIds)
        {
            SysPost post = postService.selectPostById(postId);
            if (postService.countUserPostById(postId) > 0)
            {
                String operMessage = "删除了【" + post.getPostName() + "】岗位";
                String errorMsg = String.format("%1$s已分配,不能删除", post.getPostName());
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMessage,3,errorMsg,"");
                return AjaxResult.error(errorMsg);
            }
        }
        return toAjax(postService.deletePostByIds(postIds));
    }

    /**
     * 获取岗位选择框列表
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        List<SysPost> posts = postService.selectPostAll();
        return AjaxResult.success(posts);
    }

    /**
     * 拼接部门层级
     * @param sysDept
     * @return
     */
    private String getDeptBreadcrumb(SysDept sysDept) {
    	if(sysDept!=null && sysDept.getDept()!=null ) {
    		return getDeptBreadcrumb(sysDept.getDept())+">"+sysDept.getDeptName();
    	}else if(sysDept!=null && sysDept.getDeptName()!=null){
    		return sysDept.getDeptName();
    	}else {
    		return "";
    	}
    }


    /**
     * 获取用户的岗位信息
     */
    @GetMapping("/userPostSetList")
    public AjaxResult userPostSetList()
    {
        List<SysUserPostVo> posts = postService.userPostSetList();
        return AjaxResult.success(posts);
    }
    /**
     * 获取岗位信息
     */
    @GetMapping("/postSetList")
    public AjaxResult postSetList()
    {
        List<SysUserPostVo> posts = postService.postSetList();
        return AjaxResult.success(posts);
    }

    /**
     * 获取赋权岗位信息(正在启用的岗位)
     */
    @GetMapping("/getPostAuthorizationList")
    public TableDataInfo getPostAuthorizationList(SysPost sysPost)
    {
        startPage();
        List<SysUserPostVo> posts = postService.getPostAuthorizationList(sysPost);
        return getDataTable(posts);
    }

    /**
     * add by nieyi 给岗位添加用户 2024-4-19
     * @param post
     * @return
     */
    @PreAuthorize("@ss.hasPermi('system:postNew:addUser')")
    @RepeatSubmit(interval = 3000)
    @PostMapping("/batchUserPost")
    public AjaxResult batchUserPost(@RequestBody SysPost post){
        return AjaxResult.success(postService.insertBatchUserPost(post));
    }

    @GetMapping("/fastList")
    public TableDataInfo selectPostFastList(SysPost post)
    {
        startPage();
        List<SysPost> list = postService.selectPostFastList(post);
        return getDataTable(list);
    }
}
