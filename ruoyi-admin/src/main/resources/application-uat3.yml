# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.1
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）  测试环境 /datafile/mgrdbtest/uploadPath   生产环境/datafile/mgrdbprd/uploadPath
  profile: /datafile/mgrdbtest/sjptuat3/uploadPath
  logpath: /datafile/mgrdbtest/sjptuat3/logs
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证    BLOCKPUZZLE 滑块验证 CLICKWORD 文字点选
  captchaType: BLOCKPUZZLE

# 滑块验证码
aj:
   captcha:
      cache-type: redis
      # BLOCKPUZZLE 滑块 CLICKWORD 文字点选       禁用：DEFAULT默认两者都实例化
      type: BLOCKPUZZLE
      # 右下角显示字
      water-mark: ruoyi.vip
      # 校验滑动拼图允许误差偏移量(默认5像素)
      slip-offset: 5
      # aes加密坐标开启或者禁用(true|false)
      aes-status: true
      # 滑动干扰项(0/1/2)
      interference-options: 2
      # 接口请求次数一分钟限制是否开启 true|false
      req-frequency-limit-enable: false
      # 验证失败5次，get接口锁定
      req-get-lock-limit: 5
      # 验证失败后，锁定时间间隔,s
      req-get-lock-seconds: 360
      # get接口一分钟内请求数限制
      req-get-minute-limit: 30
      # check接口一分钟内请求数限制
      req-check-minute-limit: 60
      # verify接口一分钟内请求数限制
      req-verify-minute-limit: 60

# 开发环境配置
server:
  # 服务器的HTTP端口，测试环境为21007 生产环境为11007
  port: 41007
  ssl:
    key-store-password: vCHwntQf    #填写jks-password.txt文件内的密码。
    key-store-type: JKS #JKS格式证书密钥库类型。PFX格式的证书密钥库类型为PKCS12。
    key-store: classpath:/ssl/uat/oatest.jhrs.top.jks   #您需要使用实际的证书名称替换domain_name.jks。
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
#优雅停机
management:
  endpoint:
    shutdown:
      enabled: true
  endpoints:
    web:
      exposure:
        include: "shutdown"
      base-path: /monitor
# 日志配置
logging:
  level:
    com.ruoyi: info
    org.springframework: warn

# Spring配置
spring:
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
#      master:
#        url: **********************************************************************************************************************************************
#        username: root
#        password: 1234
      master:
        url: **************************************************************************************************************************************************************************************************************
        username: mgrdb_fanfei_test
        password: sCuMmK1FBxF#%DYL
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: true
        url: *************************************************************************************************************************************************************************************
        username: cfund
        password: "@!opJ2qQYN99S#3^14PHz3zKQDpSTY"
      #数仓保证金数据源
      spark:
        # 从数据源开关/默认关闭
        enabled: true
        url: ********************************************************************************************************************************************************************************************
        username: oa_data_test_r
        password: gZI^Nr@2*eAs^AIt
      # 数仓数据库
      sparkdb:
        # 从数据源开关/默认关闭
        enabled: true
        url: ********************************************************************************************************************************************************************************************
        username: yuqiang
        password: g0vT@I3t0uVHR$h8
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: zbgx
        login-password: L^QzbR4UQXkMngB2oIE!*SqfV!&E3
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  2048MB
       # 设置总上传的文件大小
       max-request-size:  2048MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  #工作流
  activiti:
    check-process-definitions: false
    database-schema-update: true
    history-level: full
    db-history-used: true
  main:
    allow-bean-definition-overriding: true
  # redis 配置
  redis:
    # 地址
    host: **********
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 4
    # 密码
    password: c07e5d2af989ddf7e8eaefc0bca4924d
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  jackson:
    default-property-inclusion: always

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: L^QzbR4UQXkMngB2oIE!*SqfV!&E!aLL
    # 令牌有效期（默认30分钟）
    expireTime: 300

# MyBatis配置
#mybatis:
    # 搜索指定包别名
    #typeAliasesPackage: com.ruoyi.**.domain , org.ruoyi.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    #mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    #configLocation: classpath:mybatis/mybatis-config.xml

#u8cAPI调用参数设置
u8c:
  ip: http://*********:8088
  userCode: test
  passWord: 14bf3167f45d76d1eb1cb0f9e5243436
  system: test

# MyBatisPlus配置
mybatis-plus:
  # 搜索指定包别名
  type-aliases-package: com.ruoyi.**.domain,org.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  configuration:
    call-setters-on-nulls: true
    # 自动驼峰命名规则（camel case）映射
    mapUnderscoreToCamelCase: true
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    autoMappingBehavior: PARTIAL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: false
  # 请求前缀
  pathMapping: /uat-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/system/inter/trends/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

urule:
  url: http://localhost:8086/check/common/checkexcel
cdlb:
  fileUrl: /datafile/mgrdbtest/sjptuat3/uploadFile/cdlbFiles
#E签宝签署配置
esign:
  project_id: 7438901152 #项目ID(公共应用ID),正式环境下贵司将拥有独立的应用ID
  project_secret: a928c35bfb8cb7fd5d86fcef3fc6115e #项目Secret(公共应用Secret),正式环境下贵司将拥有独立的应用Secret
  api_host: http://smlitsm.tsign.cn:8080 #e签宝环境地址 模拟：http://smlitsm.tsign.cn:8080    正式http://openapi.tsign.cn:8080：
  org_id: 64CE6F8206614DE79F986307AE5FF3D5 #签署者账号标识，以此获取账户的证书进行签署
  org_sealdata: iVBORw0KGgoAAAANSUhEUgAAAcQAAAHECAYAAACnX1ofAAAd9ElEQVR42u3dW5LjuJJF0Zz/2HpO3V/XrOx2ZQRJOAB/rGOmn6oMSfTH2XSIBP/8IaKj+t8/f/7nyUukiIioNMgyvmSPiIjGQA8oiYgI9MCSiIiADyyJiAj8AJKICAC9AJKICAC9AJKICAC9AJKICAS9AJKICAC9AJKICAS9gJGICASnmbxYgSMRASFDlgdwJCIQZLbyJV9ExFSZqJzKKRExTCYp9/JORMyQEaoJNUFEDI/ZqRe1QkSMjampIzVERAyMeakvtUVEjIpJqTk1R0RMiSGRWiSi8eYj+gSMRDTWbESenLgRERASqWEimmgiok7qmojGGoaIEzASERASqX0immgGok16QS8QjTYAkSa9oTeINDuRXtErRJqbSO/oGyINTaSP9BGRBibSV3qKCAiJ9JkeI9KkRHpOzxE1bkrRJdKDRM5OiQgYiTQePc2DKNCp/hRZIs2WPhciQU5aiTSYvIgjOYEl0lT0/3MzacpVR3d7WFRJI2kkQEwy4aonJ7ZEmodaAvHN91dXTnKJNMywPEwAw5djUGNOeInKNYqIruch8t92BaKKcfJLpDEAsR0Q/+04/vnf1JkTYSLNMDgnk4H43/9NrTkpJgLD4XnpCsSfjuEnGL49XvXJE4gUfZN8REyTlYD49L+9/QzVxh+IFHvB+K9OWB2A+CRu3ePDK4gUOPgNA+JP09xKbapZnkG0rbBFcy3eJyasqkD8bal0FZTqFxRJQSvmy3Hv8Dk7v/9PxxFx474a5iOkgJ3ZJTH7nX/TJUdPJsCIHlCVoEgKV+EWBsMKFCvk8O10+GVrO7XMW0jBKthCk+KOK02z5/LLUunbTQvUcl6fEUkCQ1PfcSBmzOmbXWreHJNaBkVSnIqzEBTf5uPrjjWZ8/v1Qpon+5+qZ75DilJRNsrRv73HU7Bkz3EUDN13yIOIFGKDPH2d9lZqIONk/OQY306HKo4XkQJUgAVy9fXffc3r7Vy/3VXmzXuZDnkSkcIrnq8vUFxZas0OxIgrR9U0byIFp9iGTIlfp6MMeY+owScTZsd6AUUiRQaIL+BWDYhRtQ+I/IrAUHEBYggsOwFxYq3wLQJDRQWIgf8vOxD/7W8713S3vuVflK6gqh0vIMYujWYCYgQgABEUCQxHFFHE8trEKTErEKOnwwyAP9nv/AwUaXjxrBxL5mMHxG97tAIiXwNFMBxdNCvHVWn/zsj7CjNeWBMNw65AnGT6/I0Uy0EoZo1J1J6mVX5HXD2ZWXnqBRjyOVIkY5ZYdk2XGY5lKhDf5AwQ+R1iKI6yxbHyPSOAmDHPX+NV5XfEnTnr+ns53wNFMBxQFKvf+8tSWvYH4/72796+RyYgfpn0OiyLgyH/o4vFUKkgoqFY0WC+XmSyCr5sQIyo5WlAnLI7DyiC4Zib7iO38apojCvGHwXEG1D8rd5v1dOu494Bw0lb1oEiGI4ogF1GWOm31EiwvDHO21NidK6yXzwV2QNuyQBFQBxw0/3t9+lQK9mB+PVEoOrJUfTS/jTTB0UwHH3T/QQYrt6Y/nRK7HAvYqYpNOo7RcHQfYqgKNHNN/s9Ya63Y/f0t72nNVBpx5odn/t0t5/M/f6kRiebPq+U4DEJPr1slPGp8Ss71FS5sGbXFPcbECv0/Zd8+akAFCV3wA40u5fcsi6lfb2oovKONav1XnHD75Wl75XfIac/ZBh5JLVkXHZDLENcV27KfzMpZAbiSm88AWLl/n9b+0+hyT+pTUInxWXnNJchvivbtn1dasswJUds8P0UlFXqfPeFNx4yDIpg2ACIu2KQbcn06b/9ukyYpb52LXlX24jhxG+EU59+w08lsGWMdi3rZb4/7c1y2JudX7LUWNdcRn7XyBNCDxgGRDBsNiVmNuTo7/PmoomvQLwZk4jNzDPnM6q2o7c17OwjfFXSxk2JXSeJlRvsK15YE/G4q4pAvLEbzdQL8virZJkSi5rm26smv/6OmOXqw4lAXL2txlIpn5Wk4Umasr/pKSD+BstMYDh9T2q36dDPLbxWggAx7O9v7+25sldpFSBGg7PrdNj1RJDnAqLkHGjq6N9eskyJO4GY7STnaT9U243mRP3yEb4rKabErbvYVARiRoAAIhjyX8mQjM2GEh3H07n5sh3ZT7deZJl8v3z+m7hXuVjqJ6BHHg8v4cESMTCev/3bXTk8DYrftvCq9KT1SRfUPN2aLXJ7Ql7CiyVhcDyzLO/thMUTIL79rlX2be00HYIhPxZ8wS8JxEwbXr+dEL8C8cSxrjzKKTMQI/p8tb55CV8W9OGxrT4VPv3s6FrKDMTVZyBW7PHIJXBewpsF3JRYdir8WkMrcHj6eyUgrvf417rrtjEBjxZswS4wJWbKxZuNu1fhcPoJ89G/C2Z4bFX0BU47HghMfFqQTYnlYPjk+CLvyfuy32nW6TAjEHedJOx+aDa/5tUCPAyIUX9/elnxNBB3LUlHA/FW/a3EbfXf8xMDDBjSMhR3PFKnExB3wSb6NooMW+ut1ENU/fITvu1MQ8xfx7TKb4/ZgHjq0VsRV8qeqLuI7+KeQ1AUUAHdAowb27rdAmL0ZHdqSow+Ycl6IdSNvUr5CQ8XSLF/9IT4L8ZcGYiZp8QdUMnmAaZDPi6QhQLZJZk3r7Y7kdtsF9bciOlv/z7jagUY8nIBLBTALok9fQ/dT4a0I5a3gHijflf3Za0yHVoq5eeClzB43aB44uw58nL7rEA8BfunJl/tSRamQ74ucEUD1xGIJ+B0areQExfW7NgSLroGK5xUgiFvF7DiAeuQ3Kg9PbMsiUVOgW8gc+OJ9KsbX1eaDi2V8ncBKzYddoJixBWLt274j5oCnyxB3toRJmID7Cybr5sOz5zk8ngwPLrUUzXJEUttkbk7NSV+ebZexJ6bO/rn1N93mA6neCuvB8PrZ2AVk7zr4pIMzf112XNHfUZucxf1ANzbG3ufhmFHw816jIA4eDqsDMbIC12qAvEJLG5sZr6jL271mqXSc37K88EwDQyrQjHCVLJNiU+XRZ+ccUfBaLVXdvZj5unw9u/RlbyU7wPi9UmqMhh3PUEhOxTfTsYnb64/fe/i6c0Ydp4UdDTZzhcgjoLilOmwwzHuMLTqQFxd9qzUlxlg+OSEJPqpH92mwQ5QBMQBvxtWTn7VpdPVC1AmrdxknA6j/39nEGaEoSmx6XQYddbdBYonzfYWEP801+ml2d0nXdVNNWqp2GBkOrwOwx0TZXUDzTAl2g1j//S9A4ZZLkDKPA1WmorHT4mdNvA+OVF2gmL0b4CrVxtC4Lnp++R0U3lDjB2bLRiQHHyp6bB6Q584e428FxAM950wZHjKRrV7fneBsOIJ9QgoguH5SSjzUtvKVYZRcIW+c9NZ9h1wqk6DmWIOiEMO+vZViR2hGHm5vF3ye5t9Fxju8L3f3gMUmxxsxkI+OR1WnRZ3XU5/6uSD9vb7pOnw1F64t569CYiDp8ObMKwExYil01tPjqBzdZrppv8MENx94V1HIJbs98oHGrWMccNwqkAx+ncUOMrZ8yf6I8Ojq75sxHGi902JDjIFDHfdFtDpd1cPBu0Lw+75/vpb+I0rz92X6ACPnm1G7pLx5feBzA8D7XAGS7F9M+VE6NZUmGWKNiUWPriIwo1e4vj6JPdKsTQhAumUxzKdhKkp0YFdL9yo9z/9vLjbRgeKoNgl/5mmwqc9mfVEeiwQq57N7dqrdOdSa1aTA0SqXAMn7i3M8j0NUwMOaPclzV+XQisXvKVTmjAtVgBhBSC2mxI7Pe9wF7TeXKLdoejdZE8766bTTy7TYdhqSuw07r7ZFil6iuu2y02nm4gpz7SY8Ub8KL887c0GKweRAiZfYxNxA3xmKEIBvfWWDL4Q8dzGXZsWdBhISh1L1/tIbhXsl7hmBaPfB6mDsT/t1bfv97fPqL46NJYn3bfeuX31V1RsQZE6gjHD50Z+n07XEABi4x3Ldx3L6p6IEdMiKFLVPszS8xluou+8PV4Jrkx7yGP0Mb1ZJn0b/2xnkYBIO+op0zS64+HgU6ZCQLR08ydyl5sv8b8JRZZOEfWU7aT39D3EnfeGBcRB0+LX3/3evu9vuTidJ0CkqSe6Xz10ylRYmi+TYXhyaWXXE7a/LtNmPKsnunly+7W/v16sM9lbAbFhQ0WBLfpvp+WNaPdm2qt9dnM1pyIUfdGCULyx1VOlm/mJqk2FT6EYdbvUlJ4sMXiZDu81WsSP71kMg6jTVPjFJ/UhIILigImUaGJfvvHHLOAGxcNfUAPenRajNyAGRjIVfvdEJ6KNgGg6rDUtrt4X5Wz1TI2IQr3+O+GFei35EAaItabFlenwt8+RVUCcNhV+fRqDvmnIHcmvOS1+/XtnredqQiRy9tkbn+OJgCj5yadFBl2nFkQjR2998bcsO0GBYmIgSuEdKO5YKiVA7NpPK772dGKU72YDmemwDhh3LZXS3tyLyN4eiv7N7+nE6MQTECX8EhTBsHbeRSXmRHDnRS9vlk+f9JsaKMghMMwPRkulgEh7L275uiH3m+8og8mBaDqsAUXT4cxVAcqRr4jfLkUaECmgWcCwT2OLTr1ePP0EG/koBEQpOztFWCoFRMrRf05KB0yJgJj3DPX07420v59EaAYM//keIl6ESZZLaxUAGPbIpSjlBmFkL8l3YyBKVZ5m/e19RBQQ6S4MCRDpIhjlqs+kT/f6Sb5qATEkR5ZL+zaxCOZvZNECQ0o0rAFir4aWI0CktVUWOQJEDdygueVpYANTKAwBsXY/LedLA/crEFEa1MAUBkIwHN5PmrdfkYgOIIp9DAzlZtiqi+adbQB0H4bytTcXK/kTRUDUuKBIh4EoXzlz54Rh2KpLtaZlIMAIiKR3ZvnokX6q0rRMRGN3h6EcmQ4B8fLwVqlpGcpavEQkPxDlyUkkKBYC4oixWcETIMrbkFxYNi0MxMymk7WRmG2dupSjO3mrDAlADOqnqs369HllO4+toqkxW0Akqg5/QFxI4E5zYmi0A4hqiACxABC7JDD6uJkZRcJQDc2oj4zfrWuvAWJgkDsWFQEi5akRUyIgpjubiYLiqTiwmL5AlN/Z9QKIh9k14ffDk+99Kk5McwYM5VbtnKwFQLRcugWIp4uApQAiza0jQATEUUBknHOBKLdq6mQNVXoyCCAOAiLzBEM5pZN1Vq1Ww7+fC2qexWMXEJkmo5JbAsMkU6ILar7H43bBsohZZiXK6gkQNzKsYlPu/j6KjLLWjEirn6d1cXIXrwpPP9oCxApFs2P6ZGIEiJQVfr99zu5VuCpsaAPEG5t17/xObIHJqR21sTvv2W8fA8RERRZ9VsXoGB4gqoWbS5+AOACIJya2yLMxxscEAVHeMyx9AuLL+Fe7v6TCUutv78UMGSMozoTfabBV2IIy1ZSYsRF3f49dBvQGfJENx65mwVDO8+c0I9hMib/kKHMj3tq/b8cTLlYah2ECovwWXHoDxP5AzHygt5drVyGXoVmpDhDl9k5eo94XEAGx3NT4358dVThR35tpzoWh3NavmRN/B4iNgZgZBpEX4ZycbAkQ6U7dnP67039boR/bAPHNcVQo8l3fm3n2BaK8AqIpMXDI69Z8Xzbivl3kO6dF6g1D9QGIgAiIe4Oy+Hm7vjtoeE17AWKOVajOy6btYRhlyreA+NP3BkUvMOy1uhD9d7t3ysmeJ0A8ZMw3zqBA0QsITYl/+7uTce66x/VYIO4w6Fvf1+9uXmB4v5ZPAzHrPs0mxMZgPH3mBYpeQDhjunvzHruemhHdt5ZMB4HxxHLrqd8fQNELDHMCsUIMu9yCB4ibfrO7WcBd8gIIYFi5Zm/UfNXjBsRGRn2i2EHRCwj7+UKnWLntYvgNwLcfGnxq+QYYvcBwffXIFbaACIoJlkxA0QsMa9UkdCWDogTmWaKMaC5Q9ALCnPUIUYAIjJcacWpRe4FhumU4AkRQvJ9kUPQCwl79T4BIHxM/ubi9wJD4IiBS6zNXoAFDIkAkAkUgJAJEImAEQyJAJAJFMCQCRCJQBEKi7UBU/ASMXmBI/AAQSRN4ASHxAkAkjeAFhsQHAJE0hBcYkv4HRNIUXvqe9D4gkubw0vOk5wGRNIiXXif9DohEoKjPSa8DIhEw6vG4mhENQFQEBIpAqGbEsScQP/8RETCCoVoRX0Ak0jxgqFbEHRAlkjQQEBJIAqKkkUYCQzpbRyIJiO2SIDKACIhkmgREwRY/QAREMk0ComJVrIAIiPS85kyTgAh+BIagqN4e5EYNAGL42deuomI4gAiI9LW+btcuIDYP2GkTYTiACIj0tcaseAFiOnOL+ExtD4agSLeBOHWa/HwsXZvo1vEwG0AERKoAxc7TJCC+CMyJz9PygAiI/eul4kn7hGkSEJPAKtvyB9sCQ/WRe7WpYv6z1xcgJpreMv5AztYAUV2cr49qNVhtGADEIUBkfIAIiD3rJNNnRdaG3xABMeRzGBwYgqJ6yXxMgNgYiLcurGFkgAiIvesmuqYqwXEkEE2JljsBERDpdyjeXnI9DccWQLRsCn4MzUsl3Ft9qpDDMfchAuId82QhgAiIoFhxmux47yQgmvoIENVocihmnia73IMIiP84jl2mwmjAEBRn11XFWo787JLT4RQgnjYPJgOIgKi2TsV118OFR91y0RWI2bY2Yg+A+Ft9AaI6O7Ui1b1eQo6j2v0xmeBnSmRSkQ+XBkR1lWHJc9yTLioDsULyGA3junGfGij29qRdq1nRxweICQvvJqiYDCDe3McSEHN7UZaHkkfXpgkxyQGuLL0CIkPrvJQFiHfq5oQX3fz76sul5YC4+x6aU9+dncwF4gR4q5e4nWoqArFdrqY1jWVTBlcJLoCYv1YiP6cK0ADRFm6fP4fxzIGh7z6rbnZ8xolVKEC8uDQwCYgMaC4QHcOc2jmR1598q/Lvj4CYtLgzmBO76QFExzGnfk7m9WnuAREQj02JDAcMO9+Xqz7zry5Fr0BNvqAGEB8mlLEAYqUcg6JavXlbUMvpcDIQmQeTqZ5zQJxRq5l8DBCb/obIKMCwQw0AYr96zXzCD4gDHwWlNQHR015IH+SvkS3fu3vDaHxGMAEOgEgn6wUQCwNR+Wv2KWAARDpZO+2+q4aZUeAa28Vjepyi66nVdFjtrIC+51QDz6prQKTOJ9dba1rD9DZBMZhb02Lx97hwjDl1DYgKZGSu1DIoVp16KEk9axYwnNgs4gKI+kYtO4PU1GCoXgBRDADRGWT/4hALdStO67FQKYAIiGDojF+8TInqpQUMAREMxUNsAMGkDIgr+fM7IiB2bBSVInZve0j9AKIpEQzFRAxHxfDpU+pVBiACYtFGnmiC6lIso+teTdWHISAOLIxduZ4WD5oVz7cP4lUVw6bDKwSm42fwnR/xpTrENbqPeN1gIJoS+569e7wXie/7k0p+VxOGgAiGITkVZdJH+6Coz5oDUXLPF4WTG6L902HEyeiu9wHEQ14GiD3Pav/2t/JI+siUCIgmi5bT4ds8ySMB4lko6rliQxog5kh2FAydrRLtuV3JlAiIoHgQhv/5d9Eg1JjUpYdOQyjC+3hnoQENEHOA8MT7yB116qUTAIryP/5ZiEWgeCfBq+8jwqSf/n7x2GkQ734PQATEllPhrotmiJjmnh4xJeYD4pwv0/ws9u2yj7gTvfOqE58Lio2HMlPi3qnw6bKOeBPlnBQj3ltPA6Kp8Je/+/I+f3vIaeRLZomR7oWiDCZmD8PcOxVGLv3sgqD80hRTjXyKDCgC4uhk7myur/ciyh3Ren+cgKJeBMT2SYt478g8sUaifd4GigOY0x2IOx60GxnzU5MpEa1BaseGG4BYHIjVkhi5S8yN7wWARPkNHRCbDGCdgRh54cvO78ZmiEARDJOwpisUo68EBUUiAkVALAfEyO99YjcMkCQCRUBMEo9OQIz+3qemxAyxdgEPESiOH7o6TYk7vvctKFaPG9HUqccJKCCmC3wk1E7slXgrtu51JLpzEtm9z8qyZcKN+lmmxGzxtEEDUS7vBURfvAwUO0yFE3NPBIp8hSkGTnon9zHN0IigSASKI5liSowHavbYvXn8FCgSgSIgmhI/ATV7zE7v5UhEM6DYhiWguA7FKrFy7xTRM98DRUBsf4HFjqXTKjGyYz9Rbt/rsm1meYaYEt8DteoGBqu10X3ph0Dxlgd22zoTEIsdY/VllmzTIRjWhkPnz9sBxB313m2nsLLsmLTpd7dbKcCQKgEq81XXGeBYebcwQCx2PJGQqHRSsBq/bnGhmBOn7J8VCZ6TcKzgu+1XFrvucfrTxTAVGzkTDE2H/YC4O4+Td2i5tbqFF0MP8M33/Fqknc/4wRAQT/9ONjGWk04CRgEx8712b5ZAnrxvxVzuNCswnAdEUIyN6yQgOtACyz6dH8Gy26QA0VQDivPgOGI67HCwq+v1nRoPDOnECSUgxvusockBh3/vE3/XuUEZmRro+plV/NbA5KBDvvfpv50MQ0A0IVb7TGA0HbY/8Mj7fsDQGT1T/v479Kq/yEGdZ6kCYtKDj/jsaU15EoaMrj8UV5bcQTH/5DwWhpMDMPmqt5smSb2A+G9/t/r/J+bBgGRKLL1kZDr0u9BUKD7ZGertc0VlAgcEw5RYCoZjmwMUH/2N36gxQDCaHPd0o8v0aByqsVoSvY+nTPB/QUlwzEwuBohsxZR4CsbE+wWF0sJQHYDiiZ2TiO8LDm0zmag8qgFQBEV+L0gKeTwMCRAtnfJ6QVLEpc1M/siUKM98XrDAUN4IFOWYx+8NlkLOb2DyRbvgBIpgKGiCZjokUNwARPXJ1wWPwJBAUY3ydMGjXeYhR3QKTqtPTFGr/FwQyXRIo6dEfsLLBZKW8wCGVBmKT5+gIRM8XDDFfCnOckKZoag++begUkhswZCqAlFt8m6mTX9W4meplDpAUV2CoeDSqxiumpEcUFYoRtakGjfEgGLj+EUYkdhTdiDe/D58hlcLdPLYRTa9uFMmKJoODS6CLeAhxgCGBIpqnD87A2kdsx3NL9aUFYhRU50aB0OBbxazU2fhqpQyQvE/f3vihJIn82QJGGg24kvVoGgqzOHHIgeKrYxGXKkrFNU1H3ZmIp5gSKOBqK7BUELIUimNh6J65r0SQ2BIo6Gons/5rsiBYitDEUfqBER1zG8lSILCpkOxpKpQVLu8VqIkKgyGzrKpYj2rVx4rYRK2ZAzRT8UgulHPev+8t4oxKI6ZDhkOVanrk33fqe7BUPLAMPDfawrKDMWdn2/IIFBsBMSIK/hUJGWrcc9JBENQlMzju9FoDMpc53a54aGgODShbsAnUKz/WbyTriS1+w/jYEigGPsZfJNXgCIYEo0DYsc+AUNQbJ3kL8ejyAkUZ02FYAiKI5INhkRxdT3pJxUwBMV2SbdUShR3xXTH3gBDjTEm+aZDorX67twXrrGgUVAEQ6LvdQ6GYAiKjQrB3qNE3yDXvSfAkBTEg+NXGTQZiBP6AQxJYZgOiX6E3sTtG8GQwopj0i0ZRFN6n9eBocYYDkWVQNP7nseBIQ0uGMVOet5kCIakcP54PBPpd7dWgCEpIEAk+mM7NjCk7YWkmIhq9j0P418EikTEu3gXKSwi4lk8ixQYEfEqPkWgSEQ8ij8RKBIRX+JLpPiIiB+JJClCIuJDRAqSiHgPkcIkIp5DpECJaK7P8BpSrETEX/gLKVoi4it8hRQvEfESovqFrJiJeAj/IAWtqIl4B98gha24ifgFvyCFrtCJeASPIAWv4Il4A28gha/wifgBPyCNoBGIeAAPIA2hKYj0vb4nzaE5iPS6XifNolmI9Lf+JtI0RHpaTxNtbyBNRKSPiTSUhiLSu0QaS2MR6VciTabRiPQokabTdET6kkjzaT4ivUikGTUjUcXe039EmpNIrxFR/kbVrKS/9BaRxtW8pJ/0E5FG1sikf/QPkabW2KRn9AuRJtfopEf0CJGG1/SkJ/QEERNgAqQH9AARU2AMpObVPBGTYBSkvtU3EeNgHKSW1TMRMRNSt2qXiBgMqVN1SkRZDYfpkJokIibEhChR/alBIkppTMxJnakzImJYjEtNqSciYmIMTe2oGyJiboxOjagPImJ8DFAdqAEiYooMUo7lmYgYJxOVP/kjIsbKaOVGbogIHLu9xBgIiYgYtxcIEhEBoxcIEhEBpBcIEhEBpBcAEhGBoxcAEhEBpBcAEhEBpBcAEhEBpBcAEhEBJfARERFYAh8REQEl6BEREWUGqMgTxej/ABGejfTVVSmOAAAAAElFTkSuQmCC #印章图片Base64，若为空最终签署后将没有直观图片展现
  project_env: sml

# 企业微信配置
qiyewx:
  url: www.baidu.com
  corpid: wwb5a11409dcbcb9df

  #数据平台应用码
  datasecret: Yb1SMsf4dbjv_Wfg4aRHg2CADFK7p_M87C52eKY6LwY
  #数据平台企业应用id
  enterpriseAppId: 1000002
#企业微信回调参数
wechat:
  #回调tocken
  callbackToken: PZqRK
  #回调密钥
  callbackEncodingAESKey: dJ2Dpwct4KnAtst3t18FjcxJ3af3k9zR5ue6csRV4Li
  #通讯中心应用码
  communication: kt5OAftDP6Ywu0trdlvSzXN4XEVwUcaTgr53MKcPEXU

#OSS连接
oss:
  endpoint: oss-cn-beijing-internal.aliyuncs.com
  accessKeyId: LTAI5tLQrMvWrbDFy3vMk2Wp
  accessKeySecret: ******************************
  bucketName: datadboa
  uploadPath: sjptuat/upload/
#邮件配置信息
#服务器地址
spring.mail.host: smtp.qiye.163.com
#服务器端口
spring.mail.port: 465
#邮箱信息
spring.mail.username: <EMAIL>
#Gr9jhV2yUvpnmu7b
spring.mail.password: Gr9jhV2yUvpnmu7b
#smtp协议相关配置
spring.mail.properties.mail.smtp.auth: true
spring.mail.properties.mail.smtp.starttls.enable: true
spring.mail.properties.mail.smtp.starttls.required: true
spring.mail.properties.mail.smtp.socketFactory.class: javax.net.ssl.SSLSocketFactory
wechatApp:
  ZBGX:
    appId: wxb576675e9b71175d
    appSecret: 6f0741bac70f4af3101c9d035eb0a4d3
  HBFC:
    appId: wxb576675e9b71175d
    appSecret: 6f0741bac70f4af3101c9d035eb0a4d3
sms:
  ip: http://*************:8091
