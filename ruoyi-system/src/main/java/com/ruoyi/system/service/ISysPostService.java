package com.ruoyi.system.service;

import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.SysUserPostVo;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 岗位信息 服务层
 *
 * <AUTHOR>
 */
public interface ISysPostService
{
    /**
     * 查询岗位信息集合
     *
     * @param post 岗位信息
     * @return 岗位列表
     */
    public List<SysPost> selectPostList(SysPost post);

    /**
     * 不包含权限标识，过滤多余信息的岗位列表接口
     * @param post
     * @return
     */
    public List<SysPost> selectNoPermissionPostList(SysPost post);

    /**
     * 查询所有岗位
     *
     * @return 岗位列表
     */
    public List<SysPost> selectPostAll();

    public List<SysPost> selectPostByleader(Long[] leader);

    /**
     * 通过岗位ID查询岗位信息
     *
     * @param postId 岗位ID
     * @return 角色对象信息
     */
    public SysPost selectPostById(Long postId);

    /**
     * 根据用户ID获取岗位选择框列表
     *
     * @param userId 用户ID
     * @return 选中岗位ID列表
     */
    public List<Long> selectPostListByUserId(Long userId);

    /**
     * 校验岗位名称
     *
     * @param post 岗位信息
     * @return 结果
     */
    public String checkPostNameUnique(SysPost post);

    /**
     * 校验岗位编码
     *
     * @param post 岗位信息
     * @return 结果
     */
    public String checkPostCodeUnique(SysPost post);

    /**
     * 通过岗位ID查询岗位使用数量
     *
     * @param postId 岗位ID
     * @return 结果
     */
    public int countUserPostById(Long postId);

    /**
     * 删除岗位信息
     *
     * @param postId 岗位ID
     * @return 结果
     */
    public int deletePostById(Long postId);

    /**
     * 批量删除岗位信息
     *
     * @param postIds 需要删除的岗位ID
     * @return 结果
     * @throws Exception 异常
     */
    public int deletePostByIds(Long[] postIds);

    /**
     * 新增保存岗位信息
     *
     * @param post 岗位信息
     * @return 结果
     */
    public int insertPost(SysPost post);

    /**
     * 修改保存岗位信息
     *
     * @param post 岗位信息
     * @return 结果
     */
    public int updatePost(SysPost post);

    Set<String> selectPostCodeByUserId(Long userId);

    /**
     * 获取岗位部门人员集合
     * @return
     */
	public List<SysUserPostVo> userPostSetList();
	/**
	 * 获取岗位信息集合（含公司及部门信息）
	 * @return
	 */
	public List<SysUserPostVo> postSetList();

    /**
     * 获取生效的岗位
     * @return
     */
    List<SysUserPostVo> getPostAuthorizationList(SysPost sysPost);

    /**
     * 根据用户ID获取岗位信息
     *
     * @param userId 用户ID
     * @return
     */
    List<Long> selectPostInfoByUserId(Long userId);


    public List<SysUserPostVo> getPostListByUserName(String[] userNames);

    public List<SysPost> selectPostsByUserName(String userName);

    /**
     * 通过用户ID获取用户的主岗位
     * @param userId 用户ID
     * @return 主岗位ID
     */
    Long selectHomePostByUserId(Long userId);

    /**
     * 给岗位添加用户
     * @param post
     * @return
     */
    int insertBatchUserPost(SysPost post);

    /**
     * 根据用户ID查询岗位权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectPostPermissionByUserId(Long userId);

    List<SysPost> selectAllPostInfo();

    List<SysPost> selectPostByCodeList(String postCode);

    List<SysPost> selectPostFastList(SysPost post);
}
