package com.ruoyi.system.service.impl;

import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.system.domain.SysPostMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.SysUserPostVo;
import com.ruoyi.system.domain.vo.SysUserVo;
import com.ruoyi.system.mapper.SysPostMapper;
import com.ruoyi.system.mapper.SysUserPostMapper;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.ISysPostMenuService;
import com.ruoyi.system.service.ISysPostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 岗位信息 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysPostServiceImpl implements ISysPostService
{
    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysOperLogService sysOperLogService;

    @Autowired
    private ISysPostMenuService sysPostMenuService;

    /**
     * 查询岗位信息集合
     *
     * @param post 岗位信息
     * @return 岗位信息集合
     */
    @Override
    public List<SysPost> selectPostList(SysPost post)
    {
        return postMapper.selectPostList(post);
    }

    @Override
    public List<SysPost> selectNoPermissionPostList(SysPost post) {
        return postMapper.selectNoPermissionPostList(post);
    }

    /**
     * 查询所有岗位
     *
     * @return 岗位列表
     */
    @Override
    public List<SysPost> selectPostAll()
    {
        return postMapper.selectPostAll();
    }
    public List<SysPost> selectPostByleader(Long[] leader){
        return postMapper.selectPostByleader(leader);
    }

    /**
     * 通过岗位ID查询岗位信息
     *
     * @param postId 岗位ID
     * @return 角色对象信息
     */
    @Override
    public SysPost selectPostById(Long postId)
    {
        return postMapper.selectPostById(postId);
    }

    /**
     * 根据用户ID获取岗位选择框列表
     *
     * @param userId 用户ID
     * @return 选中岗位ID列表
     */
    @Override
    public List<Long> selectPostListByUserId(Long userId)
    {
        return postMapper.selectPostListByUserId(userId);
    }

    /**
     * 校验岗位名称是否唯一
     *
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public String checkPostNameUnique(SysPost post)
    {
        Long postId = StringUtils.isNull(post.getPostId()) ? -1L : post.getPostId();
        SysPost info = postMapper.checkPostNameUnique(post);
        if (StringUtils.isNotNull(info) && info.getPostId().longValue() != postId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验岗位编码是否唯一
     *
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public String checkPostCodeUnique(SysPost post)
    {
        Long postId = StringUtils.isNull(post.getPostId()) ? -1L : post.getPostId();
        SysPost info = postMapper.checkPostCodeUnique(post.getPostCode());
        if (StringUtils.isNotNull(info) && info.getPostId().longValue() != postId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 通过岗位ID查询岗位使用数量
     *
     * @param postId 岗位ID
     * @return 结果
     */
    @Override
    public int countUserPostById(Long postId)
    {
        return userPostMapper.countUserPostById(postId);
    }

    /**
     * 删除岗位信息
     *
     * @param postId 岗位ID
     * @return 结果
     */
    @Override
    public int deletePostById(Long postId)
    {
        String errorMessage = "";
        SysPost sysPost = postMapper.selectPostById(postId);
        try {
            int i = 0;
            //删除岗位用户关系记录 mashuo add 2023-08-08
            userPostMapper.deleteUserPostByPostId(postId);
            //删除岗位菜单关联关系
            sysPostMenuService.deleteSysPostMenuByMenuId(postId);
            //删除岗位
            i = postMapper.deletePostById(postId);
            return i;
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMessage = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            //增加删除岗位的操作日志
            String operMsg = "删除了【" + sysPost.getPostName() + "】岗位";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMsg,3,errorMessage,"");
        }
    }

    /**
     * 批量删除岗位信息
     *
     * @param postIds 需要删除的岗位ID
     * @return 结果
     * @throws Exception 异常
     */
    @Override
    public int deletePostByIds(Long[] postIds)
    {
        int i = 0;
        StringBuffer postNames = new StringBuffer("");
        String errorMsg = "";
        try {
            for (Long postId : postIds)
            {
                SysPost post = selectPostById(postId);
                postNames.append("【" + post.getPostName() + "】");
                //删除岗位用户关系记录
                userPostMapper.deleteUserPostByPostId(postId);
                //删除岗位菜单关联关系
                sysPostMenuService.deleteSysPostMenuByMenuId(postId);
            }
            i = postMapper.deletePostByIds(postIds);
            return i;
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "删除了" + postNames + "岗位";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMessage, 3, errorMsg,"");
        }
    }

    /**
     * 新增保存岗位信息
     * add by nieyi 2024-4-15 新增岗位信息
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public int insertPost(SysPost post)
    {
        String message = "";
        try {
            //插入岗位
            int i = postMapper.insertPost(post);
            //add by nieyi 2024-4-15 新增岗位时保存与菜单关联关系
            List<SysPostMenu> sysPostMenuList = new ArrayList<>();
            if (post.getMenuIds().size() > 0 && !CollectionUtils.isEmpty(post.getMenuIds())){
                for (SysMenu sysMenu : post.getMenuIds()) {
                    //循环组装参数
                    SysPostMenu sysPostMenu = new SysPostMenu();
                    sysPostMenu.setMenuId(sysMenu.getMenuId());
                    sysPostMenu.setPostId(post.getPostId());
                    sysPostMenu.setCreateTime(DateUtils.getNowDate());
                    sysPostMenu.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
                    //添加到集合
                    sysPostMenuList.add(sysPostMenu);
                }
                //插入
                sysPostMenuService.insertSysPostMenu(sysPostMenuList);
            }
            return i;
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            message = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "新增了【" + post.getPostName() + "】岗位";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMessage, 1, message,"");
        }
    }

    /**
     * 修改保存岗位信息
     * add by nieyi 2024-4-15 修改岗位信息
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public int updatePost(SysPost post) {
        String message = "";
        try {
            //更新岗位信息表
            int i = postMapper.updatePost(post);
            // add by nieyi 更新菜单与岗位关联关系 2024-04-16
            //获取本次修改，增加了哪些菜单
            List<Long> addMenuList = post.getAddMenuList();
            //获取本次修改，删除(取消授权)了哪些菜单
            List<Long> delMenuList = post.getDelMenuList();
            //删除岗位菜单关联
            if (!CollectionUtils.isEmpty(delMenuList)){
                postMapper.deletePostMenuRelation(delMenuList,post.getPostId());
            }
            //新增岗位菜单关联
            List<SysPostMenu> sysPostMenuList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(addMenuList)){

                for (Long menuId : addMenuList) {
                    SysPostMenu sysPostMenu = new SysPostMenu();
                    sysPostMenu.setPostId(post.getPostId());
                    sysPostMenu.setCreateTime(DateUtils.getNowDate());
                    sysPostMenu.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
                    sysPostMenu.setMenuId(menuId);
                    sysPostMenuList.add(sysPostMenu);
                }
                //数据落地
                i = sysPostMenuService.insertSysPostMenu(sysPostMenuList);
            }
            return i;
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            message = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "修改了【" + post.getPostName() + "】岗位信息";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMessage, 2, message,"");
        }
    }

    /**
     * 根据用户ID获取PostCode
     *
     * @param userId 用户ID
     * @return 选中岗位PostCode列表
     */
    @Override
    public Set<String> selectPostCodeByUserId(Long userId) {
        return postMapper.selectPostCodeByUserId(userId);
    }

    /**
     * 获取岗位部门人员集合
     */
	@Override
	public List<SysUserPostVo> userPostSetList() {
		 return postMapper.userPostSetList();
	}

	/**
	 *  获取岗位信息集合（含公司及部门信息）
	 */
	@Override
	public List<SysUserPostVo> postSetList() {
		return postMapper.postSetList();
	}

    @Override
    public List<SysUserPostVo> getPostAuthorizationList(SysPost sysPost) {
        List<SysUserPostVo> postAuthorizationList = postMapper.getPostAuthorizationList(sysPost);
        if (postAuthorizationList.isEmpty()) {
            return new ArrayList<>();
        }
        Long[] postIds = postAuthorizationList.stream().map(SysUserPostVo::getPostId).toArray(Long[]::new);;
        List<SysUserVo> sysUserPostVos = userPostMapper.selectUserPostByPostId(postIds);
        Map<Long, List<SysUserVo>> postUserMap = sysUserPostVos.stream().collect(Collectors.groupingBy(SysUserVo::getPostId));
        postAuthorizationList.forEach(postAuthorization -> {
            postAuthorization.setSysUserList(postUserMap.get(postAuthorization.getPostId()));
        });
        return postAuthorizationList;
    }

    @Override
    public List<Long> selectPostInfoByUserId(Long userId) {
        return postMapper.selectPostInfoListByUserId(userId);
    }

    public List<SysUserPostVo> getPostListByUserName(String[] userNames){
        return postMapper.getPostListByUserName(userNames);
    }

    /**
     * 通过用户ID获取用户的主岗位
     * @param userId 用户ID
     * @return 主岗位ID
     */
    @Override
    public Long selectHomePostByUserId(Long userId) {
        return postMapper.selectHomePostByUserId(userId);
    }

    /**
     * 给岗位添加用户
     * add by nieyi 2024-4-19
     * @param post
     * @return
     */
    @Override
    public int insertBatchUserPost(SysPost post) {
        String errorMsg = "";
        StringBuffer buffer = new StringBuffer("");
        SysPost sysPost = postMapper.selectPostById(post.getPostId());
        List<Long> oldCollect = new ArrayList<>();
        List<Long> newCollect = new ArrayList<>();
        try {
            //先根据岗位id查询岗位下所有用户
            List<SysUser> userList = postMapper.selectPostUserByPostId(post.getPostId());
            if (!CollectionUtils.isEmpty(userList)) {
                oldCollect = userList.stream().map(SysUser::getUserId).collect(Collectors.toList());
            }
            //获取本次修改的用户集合
            List<SysUser> userList1 = post.getUserList();
            if (!CollectionUtils.isEmpty(userList1)) {
                newCollect = userList1.stream().map(SysUser::getUserId).collect(Collectors.toList());
            }
            //去除旧数据，保留新数据
            newCollect.removeAll(oldCollect);

            if (!CollectionUtils.isEmpty(newCollect)){
                List<SysUserPost> userPostList = new ArrayList<SysUserPost>();
                for (Long userId : newCollect) {
                    SysUserPost sup = new SysUserPost();
                    sup.setPostId(post.getPostId());
                    sup.setUserId(userId);
                    //根据用户id查询用户岗位集合，如果用户没有任何一个岗位，则将当前岗位设置为用户主岗位
                    List<Long> list = postMapper.selectPostListByUserId(userId);
                    if (CollectionUtils.isEmpty(list)) {
                        sup.setHomePost("0");
                    } else {
                        sup.setHomePost("1");
                    }
                    //拼接日志参数
                    for (SysUser sysUser : userList1) {
                        if (userId == sysUser.getUserId()){
                            buffer.append("【" + sysUser.getNickName() + "】");
                        }
                    }
                    userPostList.add(sup);
                }
                // 插入岗位与用户关系记录
                if (userPostList.size() > 0) {
                    //删除旧数据
                    //userPostMapper.deleteUserPostByPostId(post.getPostId());
                    //插入新数据
                    userPostMapper.batchUserPost(userPostList);
                }
            }
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "给【" + sysPost.getPostName() + "】岗位添加了用户" + buffer;
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.POSTRE.getCode(), FunctionNodeEnum.POSTMANAGE.getCode(), operMessage, 1, errorMsg,"");
        }
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectPostPermissionByUserId(Long userId)
    {
        List<SysPost> perms = postMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysPost perm : perms)
        {
            if (StringUtils.isNotNull(perm))
            {
                permsSet.addAll(Arrays.asList(perm.getPostCode().trim().split(",")));
            }
        }
        return permsSet;
    }

    public List<SysPost> selectPostsByUserName(String userName){
        return postMapper.selectPostsByUserName(userName);
    }

    /**
     * 查询所有岗位
     *
     * @return 岗位列表
     */
    @Override
    public List<SysPost> selectAllPostInfo()
    {
        return postMapper.selectAllPostInfo();
    }

    @Override
    public List<SysPost> selectPostByCodeList(String postCode) {
        return postMapper.selectPostByCode(postCode);
    }


    @Override
    public List<SysPost> selectPostFastList(SysPost post){
        return postMapper.selectPostFastList(post);
    }
}
