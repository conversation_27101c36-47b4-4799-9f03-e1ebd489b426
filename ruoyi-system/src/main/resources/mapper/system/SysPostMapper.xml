<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysPostMapper">

	<resultMap type="SysPost" id="SysPostResult">
		<id     property="postId"        column="post_id"       />
		<result property="postCode"      column="post_code"     />
		<result property="postName"      column="post_name"     />
		<result property="postType"      column="post_type"     />
		<result property="postSort"      column="post_sort"     />
		<result property="status"        column="status"        />
		<result property="createBy"      column="create_by"     />
		<result property="createTime"    column="create_time"   />
		<result property="updateBy"      column="update_by"     />
		<result property="updateTime"    column="update_time"   />
		<result property="remark"        column="remark"        />
		<result property="leader"        column="leader"        />
		<result property="leaderName"    column="leader_name"   />
		<result property="unitId"        column="unit_id"        />
		<result property="deptId"        column="dept_id"        />
		<result property="deptName" column="dept_name"/>
		<collection  property="userList"   javaType="java.util.List" select="selectPostUserListByPostId" column="post_id"/>
		<collection  property="dept"   javaType="com.ruoyi.common.core.domain.entity.SysDept" select="selectPostDeptByDeptId" column="dept_id"/>
		<collection  property="menuCounts"   javaType="java.lang.Integer" select="selectPostMenuCountsByDeptId" column="post_id"/>
		<collection  property="menuIds" javaType="java.util.List" select="selectPostMenuListByPostId" column="post_id"/>
	</resultMap>

	<resultMap type="SysPost" id="SysPostInfoResult">
		<id     property="postId"        column="post_id"       />
		<result property="postCode"      column="post_code"     />
		<result property="postName"      column="post_name"     />
		<result property="postType"      column="post_type"     />
		<result property="postSort"      column="post_sort"     />
		<result property="status"        column="status"        />
		<result property="createBy"      column="create_by"     />
		<result property="createTime"    column="create_time"   />
		<result property="updateBy"      column="update_by"     />
		<result property="updateTime"    column="update_time"   />
		<result property="remark"        column="remark"        />
		<result property="leader"        column="leader"        />
		<result property="leaderName"    column="leader_name"   />
		<result property="unitId"        column="unit_id"        />
		<result property="deptId"        column="dept_id"        />
		<result property="deptName" column="dept_name"/>
		<collection  property="dept"   javaType="com.ruoyi.common.core.domain.entity.SysDept" select="selectPostDeptByDeptId" column="dept_id"/>
	</resultMap>


	<resultMap id="sysUserResult" type="SysUser">
		<id property="userId" column="user_id"/>
		<result property="userName" column="user_name"/>
		<result property="nickName"     column="nick_name"    />
		<result property="status"   column="user_status" />
	</resultMap>

	<resultMap id="sysDeptResult" type="SysDept">
		<id property="deptId" column="dept_id"/>
		<result property="parentId" column="parent_id"/>
		<result property="deptName" column="dept_name"/>
		<collection  property="dept"   javaType="com.ruoyi.common.core.domain.entity.SysDept" select="selectPostDeptByDeptId" column="parent_id"/>
	</resultMap>

	<resultMap id="sysMenuResult" type="sysMenu">
		<id property="menuId" column="menu_id"/>
		<result property="menuName" column="menu_name"/>
		<result property="parentId" column="parent_id"/>
		<result property="menuType" column="menu_type"/>
	</resultMap>

	<resultMap type="SysUserPostVo" id="SysUserPostVoResult">
		<id property="userId"        column="user_id"        />
		<result property="userName" column="user_name"/>
		<result property="status"        column="status"        />
		<result property="unitId"        column="unit_id"        />
		<result property="companyName" column="company_name"/>
		<result property="companyShortName" column="company_short_name"/>
		<result property="deptId"        column="dept_id"        />
		<result property="deptName" column="dept_name"/>
		<result property="postId"        column="post_id"       />
		<result property="postCode"      column="post_code"     />
		<result property="postName"      column="post_name"     />
		<result property="postStatus"      column="post_status"     />
		<result property="leader"        column="leader"        />
		<result property="leaderName"        column="leader_name"        />
		<result property="homePost"        column="home_post"        />
	</resultMap>

	<sql id="selectPostVo">
        select distinct p.post_id, p.post_code, p.post_name, p.post_type, p.post_sort, p.status, p.create_by, p.create_time, p.remark,p.leader, u2.post_name as leader_name ,p.dept_id, p.unit_id, sc.company_name as unitName, false AS selectFlag
		from sys_post p
		left join sys_post u2 on u2.post_id=p.leader
		left join sys_dept d on d.dept_id=p.dept_id
		left join sys_company sc on p.unit_id = sc.id
		left join sys_user_post sup on sup.post_id = p.post_id
		left join sys_user su on sup.user_id = su.user_id
    </sql>

	<select id="selectPostList" parameterType="SysPost" resultMap="SysPostResult">
	    <include refid="selectPostVo"/>
		<where>
			<if test="postCode != null and postCode != ''">
				AND p.post_code like concat('%', #{postCode}, '%')
			</if>
			<if test="status != null and status != ''">
				AND p.status = #{status}
			</if>
			<if test="postType != null and postType !=''">
				and p.post_type = #{postType}
			</if>
			<if test="postName != null and postName != ''">
				AND p.post_name like concat('%', #{postName}, '%')
			</if>
			<if test="deptName != null and deptName != ''">
				AND d.dept_name like concat('%', #{deptName}, '%')
			</if>
			<if test="deptId != null and deptId !=''">
				and p.dept_id = #{deptId}
			</if>
			<if test="userName != null and userName != ''">
				AND (su.user_name like concat('%', #{userName}, '%') or su.nick_name like concat('%', #{userName}, '%'))
			</if>
		</where>
	</select>


	<select id="selectPostUserListByPostId" resultMap="sysUserResult">
	     select u.user_id, u.user_name, u.nick_name, u.status as user_status
		from  sys_user_post up
		left join sys_user u on u.user_id = up.user_id
		where up.post_id = #{id} and u.del_flag = '0' order by u.nick_name
	</select>

	<select id="selectPostDeptByDeptId" resultMap="sysDeptResult">
	     select u.dept_id,u.parent_id,u.dept_name
		from  sys_dept u
		where u.dept_id=#{id}
	</select>

	<select id="selectPostAll" resultMap="SysPostResult">
		<include refid="selectPostVo"/>
	</select>

	<select id="selectPostByleader" parameterType="Long" resultMap="SysPostResult">
		select p.post_id, p.post_code, p.post_name, p.post_type, p.post_sort, p.status, p.create_by, p.create_time, p.remark,p.leader,p.dept_id, p.unit_id
		from sys_post p
		where p.leader in
		<foreach item="leader" collection="array" open="(" separator="," close=")">
			#{leader}
		</foreach>
	</select>


	<select id="selectPostById" parameterType="Long" resultMap="SysPostResult">
		<include refid="selectPostVo"/>
		where p.post_id = #{postId}
	</select>

	<select id="selectPostListByUserId" parameterType="Long" resultType="Long">
		select p.post_id
        from sys_post p
	        left join sys_user_post up on up.post_id = p.post_id
	        left join sys_user u on u.user_id = up.user_id
	    where u.user_id = #{userId}
	</select>

	<select id="selectPostsByUserName" parameterType="String" resultMap="SysPostResult">
		select p.post_id, p.post_name, p.post_code,p.dept_id
		from sys_post p
			 left join sys_user_post up on up.post_id = p.post_id
			 left join sys_user u on u.user_id = up.user_id
		where u.user_name = #{userName}
	</select>

	<select id="checkPostNameUnique" resultMap="SysPostResult">
		<include refid="selectPostVo"/>
		 where p.post_name=#{postName}  and p.unit_id = #{unitId} limit 1
	</select>

	<select id="checkPostCodeUnique" parameterType="String" resultMap="SysPostResult">
		<include refid="selectPostVo"/>
		 where p.post_code=#{postCode} limit 1
	</select>



	<update id="updatePost" parameterType="SysPost">
 		update sys_post
 		<set>
 			remark = #{remark},
 			leader = #{leader},
 			<if test="postCode != null and postCode != ''">post_code = #{postCode},</if>
 			<if test="postName != null and postName != ''">post_name = #{postName},</if>
 			<if test="postType != null and postType != ''">post_type = #{postType},</if>
 			<if test="postSort != null and postSort != ''">post_sort = #{postSort},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
 			<if test="unitId != null and unitId != ''">unit_id = #{unitId},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where post_id = #{postId}
	</update>

 	<insert id="insertPost" parameterType="SysPost" useGeneratedKeys="true" keyProperty="postId">
 		insert into sys_post(
 			<if test="postId != null and postId != 0">post_id,</if>
 			<if test="postCode != null and postCode != ''">post_code,</if>
 			<if test="postName != null and postName != ''">post_name,</if>
 			<if test="postType != null and postType != ''">post_type,</if>
 			<if test="postSort != null and postSort != ''">post_sort,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="deptId != null and deptId != ''">dept_id,</if>
 			<if test="unitId != null and unitId != ''">unit_Id,</if>
 			<if test="leader != null and leader != ''">leader,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="postId != null and postId != 0">#{postId},</if>
 			<if test="postCode != null and postCode != ''">#{postCode},</if>
 			<if test="postName != null and postName != ''">#{postName},</if>
 			<if test="postType != null and postType != ''">#{postType},</if>
 			<if test="postSort != null and postSort != ''">#{postSort},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="unitId != null and unitId != ''">#{unitId},</if>
 			<if test="leader != null and leader != ''">#{leader},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>

	<delete id="deletePostById" parameterType="Long">
		delete from sys_post where post_id = #{postId}
	</delete>

	<delete id="deletePostByIds" parameterType="Long">
 		delete from sys_post where post_id in
 		<foreach collection="array" item="postId" open="(" separator="," close=")">
 			#{postId}
        </foreach>
 	</delete>

	<select id="selectPostCodeByUserId" parameterType="Long" resultType="String">
		select post_code from  sys_user_post up
	    left join sys_post p on up.post_id=p.post_id
		where up.user_id=#{userId}
	</select>


	<select id="userPostSetList"  resultMap="SysUserPostVoResult">
		select u.user_id,u.user_name,u.status,un.id,un.company_short_name,un.company_name,p.post_id, p.post_code,p.post_type, p.post_name, p.post_sort,p.status as post_status,p.leader, p2.post_name as leader_name ,p.dept_id,d.dept_name, p.unit_id,sup.home_post
		from sys_user u
		left join sys_user_post sup on sup.user_id = u.user_id
		left join sys_post p on sup.post_id = p.post_id
		left join sys_post p2 on p2.post_id = p.leader
		left join sys_dept d on d.dept_id = p.dept_id
		left join sys_company un on un.id = d.unit_id
	</select>


	<select id="postSetList"  resultMap="SysUserPostVoResult">
		SELECT un.id, un.company_name, un.company_short_name, p.post_id, p.post_code, p.post_name, p.post_type,
		       p.post_sort,p.status AS post_status,p.leader, p2.post_name AS leader_name ,p.dept_id,d.dept_name, p.unit_id
		FROM  sys_post p
		LEFT JOIN sys_post p2 ON p2.post_id=p.leader
		LEFT JOIN sys_dept d ON d.dept_id=p.dept_id
		LEFT JOIN sys_company un on un.id = d.unit_id
	</select>

	<select id="getPostAuthorizationList" parameterType="SysPost" resultType="com.ruoyi.system.domain.vo.SysUserPostVo">
		SELECT un.id AS unitId,
		       un.company_name AS unitName,
			   un.company_short_name AS unitShortName,
		       p.post_id AS postId,
		       p.post_code AS postCode,
		       p.post_name AS postName,
		       p.post_sort AS postSort,
		       p.dept_id AS deptId,
		       d.dept_name AS deptName,
		       concat(company_name, '>' ,dept_name) AS postJoint
		FROM  sys_post p
		LEFT JOIN sys_dept d ON d.dept_id = p.dept_id
		left join sys_company un on un.id = d.unit_id
		<where>
			p.status = '0'
			<if test="deptId != null and deptId != ''">
				AND p.dept_id = #{deptId}
			</if>
			<if test="postCode != null and postCode != ''">
				AND p.post_code like concat('%', #{postCode}, '%')
			</if>
			<if test="postName != null and postName != ''">
				AND p.post_name like concat('%', #{postName}, '%')
			</if>
		</where>
	</select>

	<select id="selectMainPostByUserName" resultMap="SysPostResult">
	    SELECT sp.* FROM sys_post sp LEFT JOIN sys_user_post sup on sp.post_id=sup.post_id LEFT JOIN sys_user su ON su.user_id=sup.user_id WHERE su.user_name=#{userName,jdbcType=VARCHAR} AND home_post='0'
    </select>
	<select id="selectPostInfoListByUserId" resultType="java.lang.Long">
		select p.dept_id
		from sys_post p
				 left join sys_user_post up on up.post_id = p.post_id
				 left join sys_user u on u.user_id = up.user_id
		where u.user_id = #{userId}
	</select>


	<select id="getPostListByUserName" parameterType="String" resultType="com.ruoyi.system.domain.vo.SysUserPostVo">
		select su.user_name,sup.post_id,sp.post_name,sup.home_post,su.user_id,su.status
		from sys_user su
			right join  sys_user_post sup on su.user_id =  sup.user_id
			left join sys_post sp on sp.post_id = sup.post_id
		where su.user_name in
			<foreach item="username" collection="array" open="(" separator="," close=")">
				#{username}
			</foreach>
	</select>

	<select id="queryHomeSysPost" resultMap="SysPostResult">
		select p.post_id, p.post_code, p.post_name, p.post_sort, p.status, p.create_by, p.create_time, p.remark,p.leader,p.dept_id  ,false AS selectFlag
		from sys_user u
				 INNER JOIN sys_user_post up on u.user_id = up.user_id
				 INNER JOIN sys_post p on up.post_id = p.post_id
		WHERE u.user_name = #{userName}
		  AND up.home_post = '0'
	</select>

	<select id="selectPostMenuCountsByDeptId" resultType="java.lang.Integer">
		select count(1)
		from  sys_post_menu pm
				  left join sys_menu sm
							on pm.menu_id = sm.menu_id
		where pm.post_id = #{deptId} and sm.parent_id = 0 and sm.menu_type = 'M' and sm.status = '0'
	</select>

	<select id="selectPostMenuListByPostId" resultType="java.lang.Integer" resultMap="sysMenuResult">
		select sm.menu_id, sm.menu_name, sm.menu_type, sm.parent_id from sys_post_menu spm
			left join sys_menu sm on spm.menu_id = sm.menu_id
			where spm.post_id = #{postId}
	</select>

	<select id="selectHomePostByUserId" resultType="java.lang.Long">
		SELECT
			post_id
		FROM
			sys_user_post
		WHERE
			user_id = #{userId}
		  AND home_post = '0'
	</select>

	<delete id="deletePostMenuInfoByPostId" parameterType="Long">
		delete from sys_post_menu where post_id = #{postId}
	</delete>

    <select id="selectDataByUserId" resultMap="SysPostResult">
		SELECT  p.post_id, p.post_code, p.post_name, p.post_sort, p.status, p.create_by, p.create_time, p.remark,p.leader
		FROM sys_user_post sup
		LEFT JOIN sys_post  p ON sup.post_id = p.post_id
		WHERE sup.user_id = #{userId}
	</select>
    <select id="selectRolePermissionByUserId" resultType="com.ruoyi.system.domain.SysPost">
		select distinct r.post_id, r.post_name, r.post_code, r.post_type, r.post_sort, r.status, r.create_by,r.unit_id,
						r.dept_id, r.leader, r.create_time, r.remark
		from sys_post r
				 left join sys_user_post ur on ur.post_id = r.post_id
				 left join sys_user u on u.user_id = ur.user_id
				 left join sys_dept d on u.dept_id = d.dept_id
			WHERE r.status = '0' and ur.user_id = #{userId}
	</select>

	<select id="selectPostListByDeptId" resultType="com.ruoyi.common.core.domain.entity.SysUserPostDeptVo">
		select su.user_id as userId,
			   su.nick_name as nickName,
			   su.user_name as userName,
			   sp.post_id as postId,
			   sp.post_name as postName,
			   sd.dept_id as deptId,
			   sd.dept_name as deptName,
			   sc.id as companyId,
			   sc.company_short_name as companyShortName
		from sys_post sp
				 left join sys_user_post sup on sp.post_id = sup.post_id
				 left join sys_user su on sup.user_id = su.user_id
				 left join sys_dept sd on sd.dept_id = sp.dept_id
				 left join sys_company sc on sp.unit_id = sc.id
		where sp.dept_id = #{deptId} and su.del_flag = '0' and su.status = '0' and sp.status = '0' and sd.status = '0' and sd.del_flag = '0' and sc.status = '0'
		group by su.user_id
	</select>

	<select id="selectAllPostInfo" resultMap="SysPostInfoResult">
		<include refid="selectPostVo"/>
	</select>

	<delete id="deletePostMenuRelation">
		delete from sys_post_menu where post_id = #{postId} and menu_id in
			<foreach collection="delMenuList" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
	</delete>

	<select id="selectPostByCode" resultMap="SysPostInfoResult">
		select * from sys_post where post_code = #{postCode}
	</select>

    <select id="selectPostUserByPostId" resultType="com.ruoyi.common.core.domain.entity.SysUser">
		select * from sys_user_post where post_id = #{postId}
	</select>

	<select id="selectNoPermissionPostList" parameterType="SysPost" resultMap="SysPostResult">
		select distinct p.post_id, p.post_code, p.post_name, p.post_type, p.post_sort, p.dept_id, p.unit_id, false AS selectFlag
		from sys_post p
		left join sys_dept d on d.dept_id=p.dept_id
		<where>
			<if test="postCode != null and postCode != ''">
				AND p.post_code like concat('%', #{postCode}, '%')
			</if>
			<if test="status != null and status != ''">
				AND p.status = #{status}
			</if>
			<if test="postType != null and postType !=''">
				and p.post_type = #{postType}
			</if>
			<if test="postName != null and postName != ''">
				AND p.post_name like concat('%', #{postName}, '%')
			</if>
			<if test="deptName != null and deptName != ''">
				AND d.dept_name like concat('%', #{deptName}, '%')
			</if>
			<if test="deptId != null and deptId !=''">
				and p.dept_id = #{deptId}
			</if>
		</where>
	</select>
	<select id="selectAllUserAndDeptUInfo" resultType="java.util.Map">
		SELECT
			sup.user_id AS userId,
			sp.dept_id AS deptId
		FROM
			sys_user_post sup
				LEFT JOIN sys_post sp ON sup.post_id = sp.post_id
		WHERE
			sup.home_post = 0
		  AND sp.`status` = 0;
	</select>

	<select id="selectPostFastList" parameterType="SysPost" resultType="SysPost">
		select p.post_id, p.post_code, p.post_name, p.post_type, p.post_sort, p.status, p.create_by
			 , p.create_time, p.remark,p.leader,p.dept_id, p.unit_id
		from sys_post p
		WHERE p.status = '0'
	</select>
</mapper>
