<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.nocode.mapper.BusinessControlMapper">

    <resultMap id="companyInfoMap" type="com.ruoyi.nocode.domain.busCont.CompanyInfoBo">
        <result column="id" property="companyId" />
        <result column="company_name" property="companyName" />
        <result column="company_short_name" property="companyShortName" />
    </resultMap>

    <resultMap id="gcDetailMap" type="com.ruoyi.nocode.domain.busCont.GuaranteeCompanyDetail">
        <result column="id" property="companyId" />
        <result column="company_name" property="companyName" />
        <result column="net_assets" property="netAssets" />
        <result column="guarantee_multiple" property="guaranteeMultiple" />
        <result column="max_limit" property="maxLimit" />
    </resultMap>

    <resultMap id="projectRuleMap" type="com.ruoyi.nocode.domain.busCont.ControlProjectRuleBo">
        <result column="project_rule_id" property="projectRuleId" />
        <result column="project_id" property="projectId" />
        <result column="project_name" property="projectName" />
        <result column="control_type" property="controlType" />
        <result column="is_control_flag" property="isControlFlag" />
        <result column="max_loan_limit" property="maxLoanLimit" />
        <result column="old_project_id" property="oldProjectId" />
        <result column="old_project_name" property="oldProjectName" />
        <result column="import_type" property="importType" />
        <result column="loaded_flag" property="loadedFlag" />
        <result column="status" property="status" />
        <result column="predict_loan_amount" property="predictLoanAmount" />
        <result column="loan_amount" property="loanAmount" />
        <result column="earnest_money" property="earnestMoney" />
        <result column="margin_rate" property="marginRate" />
        <result column="margin_rate_limit" property="marginRateLimit" />
        <result column="rule_description" property="ruleDescription" />
        <result column="user_name" property="userName" />
        <result column="over_predict_loan_amount" property="overPredictLoanAmount" />
        <result column="max_loan_date" property="maxLoanDate" />
        <result column="imported_data_type" property="importedDataType" />
        <result column="create_by" property="createBy"/>
        <result column="guarantee_type" property="guaranteeType"/>

    </resultMap>

    <resultMap id="BusinessDataRecordResult" type="com.ruoyi.nocode.domain.busCont.ControlRuleProcessRecord" >
        <result property="id"    column="id"    />
        <result property="applyType"    column="apply_type"    />
        <result property="processId"    column="process_id"    />
        <result property="applyIds"    column="apply_id_list"    />
        <result property="operation"    column="operation"    />
        <result property="oaApplyRecordsOldData"    column="oa_apply_records_old_data"    />
        <result property="oaApplyRecordsNewData"    column="oa_apply_records_new_data"    />
        <result property="editUserId"    column="edit_user_id"    />
        <result property="editTime"    column="edit_time"    />
        <result property="editInfo"    column="edit_info"    />
        <result property="checkUserId"    column="check_user_id"    />
        <result property="checkTime"    column="check_time"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="nickName"     column="nick_name"/>
    </resultMap>


    <select id="queryCompanyInfo" resultMap="companyInfoMap">
        SELECT
            sc.id,sc.company_name,sc.company_short_name
        FROM sys_company sc
                LEFT JOIN company_type_mapping ctm ON ctm.company_id = sc.id
        WHERE
            ctm.company_type_code in
            <foreach collection="companyTypeCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        and sc.is_delete = '0'
    </select>

    <select id="queryGuaranteeCompanyDetail" resultMap="gcDetailMap">
        SELECT
        sc.id,
        sc.company_name,
        gc.net_assets,
        gc.guarantee_multiple,
        gc.max_limit,
        gc.warning_balance_limit,
        gc.warning_switch_limit,
        gc.warning_balance_max,
        gc.warning_switch_max
        FROM
        sys_company sc LEFT JOIN guarantee_company_detail gc
        ON sc.id = gc.company_id and gc.control_type =#{controlType}
        LEFT JOIN company_type_mapping ctm on ctm.company_id = sc.id
        where 1=1
        <if test="companyId !=null">
            AND sc.id = #{companyId}
        </if>
        <if test="companyTypeCodeList != null and companyTypeCodeList.size() > 0">
            AND ctm.company_type_code in
            <foreach collection="companyTypeCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="companyIdList != null">
            AND sc.id in
            <foreach collection="companyIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryGCompanyDetailNum" resultType="java.lang.Integer">
        select count(1) from guarantee_company_detail where company_id =#{companyId} and control_type = #{controlType}
    </select>

    <insert id="addGuaranteeCompanyDetail">
        insert into guarantee_company_detail (company_id, control_type, net_assets, guarantee_multiple, max_limit,
                                              create_by, create_time, update_time)
        values (#{dto.companyId}, #{dto.controlType}, #{dto.netAssets}, #{dto.guaranteeMultiple}, #{dto.maxLimit},
                #{userName}, now(), now())
    </insert>

    <update id="updateGuaranteeCompanyDetail">
        update guarantee_company_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="netAssets !=null">net_assets = #{netAssets},</if>
            <if test="guaranteeMultiple !=null">guarantee_multiple = #{guaranteeMultiple},</if>
            <if test="maxLimit !=null">max_limit = #{maxLimit},</if>
            <if test="warningBalanceLimit !=null">warning_balance_limit = #{warningBalanceLimit},</if>
            <if test="warningSwitchLimit !=null and warningSwitchLimit !=''">warning_switch_limit = #{warningSwitchLimit},</if>
            <if test="warningBalanceLimit !=null">warning_balance_max = #{warningBalanceMax},</if>
            <if test="warningSwitchMax !=null and warningSwitchMax !=''">warning_switch_max = #{warningSwitchMax},</if>
            <if test="updateBy !=null and updateBy !=''">update_by = #{updateBy},</if>
            <if test="updateTime !=null">update_time = #{updateTime},</if>
        </trim>
        where company_id  = #{companyId} and control_type = #{controlType}
    </update>

    <select id="queryNewProject" resultType="java.util.Map">
        select project_id as projectId,project_name as projectName from business_control_project_detail where control_type = #{controlType}
        <if test="companyIdList !=null">
            and guarantee_company_id in
            <foreach collection="companyIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryProjectDetails" parameterType="com.ruoyi.nocode.domain.busCont.ProjectRequestDto"
            resultType="com.ruoyi.nocode.domain.busCont.ProjectDetailsBo">
        select a.project_id, a.project_name, a.control_type, a.guarantee_company_id,b.company_short_name as guaranteeCompanyShortName,b.company_name as guaranteeCompanyName, a.asset_company_id,c.company_short_name as assetCompanyShortName,
               c.company_name as assetCompanyName,d.company_short_name as fundCompanyShortName, d.company_name as fundCompanyName, a.fund_company_id,a.guarantee_type, a.max_loan_limit, a.earnest_money, a.margin_rate, a.control_rules_type, a.is_control_flag
        from business_control_project_detail a
        left join sys_company b on a.guarantee_company_id = b.id
        left join sys_company c on a.asset_company_id = c.id
        left join sys_company d on a.fund_company_id = d.id
        where  1 = 1
        <if test="controlType != null and controlType !=''">
           and a.control_type = #{controlType}
        </if>
        <if test="guaranteeCompanyId != null">
            and a.guarantee_company_id = #{guaranteeCompanyId}
        </if>

        <if test="assetCompanyId != null">
            and a.asset_company_id = #{assetCompanyId}
        </if>

        <if test="fundCompanyId != null">
            and a.fund_company_id = #{fundCompanyId}
        </if>

        <if test="projectId != null">
            and a.project_id = #{projectId}
        </if>

        <if test="guaranteeType != null and guaranteeType !='' ">
            and a.guarantee_type = #{guaranteeType}
        </if>

        <if test="isControlFlag != null and isControlFlag !='' ">
            and a.is_control_flag = #{isControlFlag}
        </if>
        <if test="controlRulesType !=null and controlRulesType !=''">
            and a.control_rules_type = #{controlRulesType}
        </if>
        <if test="companyIdList !=null">
            and a.guarantee_company_id in
            <foreach collection="companyIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="addProjectDetails" parameterType="com.ruoyi.nocode.domain.busCont.ProjectDetailsDto"
            useGeneratedKeys="true" keyProperty="projectId">
        insert into business_control_project_detail
            (project_name,control_type,guarantee_company_id,asset_company_id,fund_company_id,guarantee_type,max_loan_limit
            ,earnest_money,margin_rate,control_rules_type,is_control_flag,create_by,create_time,update_time)
        values
            (#{projectName},#{controlType},#{guaranteeCompanyId},#{assetCompanyId},#{fundCompanyId},#{guaranteeType},#{maxLoanLimit}
            ,#{earnestMoney},#{marginRate},#{controlRulesType},#{isControlFlag},#{createBy},#{createTime},#{updateTime})
    </insert>

    <insert id="addWarningRuleInfo">
        insert into business_control_warning_rule
        (project_id,warning_rule_type,warning_param,warning_switch,create_by,create_time,update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectId},#{item.warningRuleType},#{item.warningParam},#{item.warningSwitch},#{item.createBy},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <update id="updateProjectDetails" parameterType="com.ruoyi.nocode.domain.busCont.ProjectUpdateDetailsDto">
        update business_control_project_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="earnestMoney !=null">earnest_money = #{earnestMoney},</if>
            <if test="isUpdateLoan or maxLoanLimit !=null">max_loan_limit = #{maxLoanLimit},</if>
            <if test="isUpdateLoan or marginRate !=null">margin_rate = #{marginRate},</if>
            <if test="controlRulesType !=null and controlRulesType !=''">control_rules_type = #{controlRulesType},</if>
            <if test="isControlFlag !=null and isControlFlag !=''">is_control_flag = #{isControlFlag},</if>
            <if test="updateBy !=null">update_by = #{updateBy},</if>
            <if test="updateTime !=null">update_time = #{updateTime},</if>
        </trim>
        where project_id =#{projectId}
    </update>

    <update id="updateWarningRuleInfo" parameterType="com.ruoyi.nocode.domain.busCont.WarningRuleInfo">
        update business_control_warning_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="warningSwitch !=null">warning_switch = #{warningSwitch},</if>
            <if test="warningParam !=null">warning_param = #{warningParam},</if>
            <if test="updateBy !=null">update_by = #{updateBy},</if>
            <if test="updateTime !=null">update_time = #{updateTime},</if>
        </trim>
        <if test="warningRuleId !=null">
            where warning_rule_id = #{warningRuleId}
        </if>
        <if test="warningRuleId == null">
            where project_id = #{projectId} and warning_rule_type = #{warningRuleType}
        </if>
    </update>

    <select id="queryProjectDetailsById" resultType="com.ruoyi.nocode.domain.busCont.ProjectDetailsBo">
        select a.project_id, a.project_name, a.control_type, a.guarantee_company_id,b.company_short_name as guaranteeCompanyShortName,b.company_name as guaranteeCompanyName, a.asset_company_id,c.company_short_name as assetCompanyShortName,a.create_by,
               c.company_name as assetCompanyName,a.fund_company_id,d.company_short_name as fundCompanyShortName, d.company_name as fundCompanyName, a.guarantee_type, a.max_loan_limit, a.earnest_money, a.margin_rate, a.control_rules_type, a.is_control_flag
        from business_control_project_detail a
                 left join sys_company b on a.guarantee_company_id = b.id
                 left join sys_company c on a.asset_company_id = c.id
                 left join sys_company d on a.fund_company_id = d.id
            where a.project_id = #{projectId}
    </select>

    <select id="queryWarningRuleInfoById" resultType="com.ruoyi.nocode.domain.busCont.WarningRuleInfo">
        select warning_rule_id, project_id, warning_rule_type, warning_param, warning_switch from business_control_warning_rule where project_id = #{projectId}
    </select>

    <select id="queryControlProjectRule" parameterType="com.ruoyi.nocode.domain.busCont.ControlProjectRuleDto" resultMap="projectRuleMap">
        SELECT
        a.project_rule_id,
        b.project_id,
        b.project_name,
        b.control_type,
        b.is_control_flag,
        a.old_project_id,
        opd.project_name as old_project_name,
        a.import_type,
        a.loaded_flag,
        a.status,
        a.predict_loan_amount,
        a.over_predict_loan_amount,
        a.loan_amount,
        b.earnest_money,
        a.margin_rate,
        a.create_by,
        a.imported_data_type,
        b.max_loan_limit,
        b.margin_rate as margin_rate_limit,
        b.guarantee_type,
        c.nick_name as user_name
        FROM business_control_project_rule a
        INNER JOIN business_control_project_detail b ON a.project_id = b.project_id
        left join oa_project_deploy opd ON a.old_project_id = opd.id
        left join sys_user c on a.create_by = c.user_name
        where 1 = 1
        <if test="controlType !=null and controlType !=''">
           and b.control_type = #{controlType}
        </if>
        <if test="projectId !=null">
            and a.project_id =#{projectId}
        </if>

        <if test="guaranteeCompanyId != null">
            and b.guarantee_company_id = #{guaranteeCompanyId}
        </if>

        <if test="assetCompanyId !=null">
            and b.asset_company_id = #{assetCompanyId}
        </if>

        <if test="fundCompanyId != null">
            and b.fund_company_id = #{fundCompanyId}
        </if>

        <if test="guaranteeType !=null and guaranteeType !='' ">
            and b.guarantee_type = #{guaranteeType}
        </if>

        <if test="isControlFlag != null and isControlFlag !=''">
            and b.is_control_flag = #{isControlFlag}
        </if>

        <if test="importType !=null and importType !=''">
            and a.import_type = #{importType}
        </if>

        <if test="loadedFlag != null and loadedFlag !=''">
            and a.loaded_flag = #{loadedFlag}
        </if>

        <if test="status != null and status !=''">
            and a.status = #{status}
        </if>
        <if test="statusList !=null and statusList.size() >0">
            and a.status in
            <foreach item="status" collection="statusList" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="companyIdList !=null">
            and b.guarantee_company_id in
            <foreach collection="companyIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="taskStatus!=null and taskStatus !=''">
        and task_status = #{taskStatus}
        </if>
    </select>


    <insert id="addControlProjectRule" parameterType="com.ruoyi.nocode.domain.busCont.RuleDetailRequestDto"  useGeneratedKeys="true" keyProperty="projectRuleId">
        insert into business_control_project_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="oldProjectId != null">old_project_id,</if>
            <if test="importType != null and importType !=''">import_type,</if>
            <if test="loadedFlag != null and loadedFlag !=''">loaded_flag,</if>
            <if test="status != null and status !=''">status,</if>
            <if test="predictLoanAmount != null">predict_loan_amount,</if>
            <if test="loanAmount != null">loan_amount,</if>
            <if test="marginRate != null">margin_rate,</if>
            <if test="ruleDescription != null and ruleDescription != ''">rule_description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_Time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_Time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="oldProjectId != null">#{oldProjectId},</if>
            <if test="importType != null and importType !=''">#{importType},</if>
            <if test="loadedFlag != null and loadedFlag !=''">#{loadedFlag},</if>
            <if test="status != null and status !=''">#{status},</if>
            <if test="predictLoanAmount != null">#{predictLoanAmount},</if>
            <if test="loanAmount != null">#{loanAmount},</if>
            <if test="marginRate != null">#{marginRate},</if>
            <if test="ruleDescription != null and ruleDescription != ''">#{ruleDescription},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="addLoanDateDetail">
        insert into business_control_loandate_detail(project_rule_id,loan_begin_date,loan_end_date)
        values
        <foreach collection="list" item="item" separator=",">
            (#{projectRuleId},#{item.loanBeginDate},#{item.loanEndDate})
        </foreach>
    </insert>

    <select id="queryControlProjectRuleById" resultMap="projectRuleMap">
        SELECT a.project_rule_id,
               b.project_id,
               b.project_name,
               b.control_type,
               b.is_control_flag,
               b.max_loan_limit,
               a.old_project_id,
               opd.project_name as old_project_name,
               a.import_type,
               a.loaded_flag,
               a.status,
               a.predict_loan_amount,
               a.loan_amount,
               b.earnest_money,
               a.margin_rate,
               a.rule_description,
               su.nick_name as user_name,
               a.over_predict_loan_amount,
               a.max_loan_date,
               a.imported_data_type,
               b.margin_rate as margin_rate_limit
        FROM business_control_project_rule a
                 INNER JOIN business_control_project_detail b ON a.project_id = b.project_id
                 left join oa_project_deploy opd ON a.old_project_id = opd.id
                 left join sys_user su on a.create_by = su.user_name
        where a.project_rule_id = #{projectRuleId}
    </select>

    <select id="queryLoanDateDetail" resultType="com.ruoyi.nocode.domain.busCont.LoanDateDetail">
        select project_rule_id,loan_begin_date,loan_end_date from business_control_loandate_detail where project_rule_id = #{projectRuleId}
    </select>

    <update id="updateControlProjectRule" parameterType="com.ruoyi.nocode.domain.busCont.RuleDetailRequestDto">
        update business_control_project_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="oldProjectId != null ">old_project_id = #{oldProjectId},</if>
            <if test="importType != null and importType !=''">import_type = #{importType},</if>
            <if test="importedDataType !=null and importedDataType !=''">imported_data_type = #{importedDataType},</if>
            <if test="predictLoanAmount != null">predict_loan_amount = #{predictLoanAmount},</if>
            <if test="loanAmount != null">loan_amount = #{loanAmount},</if>
            <if test="marginRate != null">margin_rate = #{marginRate},</if>
            <if test="status !=null and status !=''">status = #{status},</if>
            <if test="loadedFlag !=null and loadedFlag !=''">loaded_flag = #{loadedFlag},</if>
            <if test="overPredictLoanAmount != null">over_predict_loan_amount = #{overPredictLoanAmount},</if>
            <if test="maxLoanDate !=null and maxLoanDate !=''">max_loan_date = #{maxLoanDate},</if>
            <if test="marginRate != null">margin_rate = #{marginRate},</if>
            <if test="ruleDescription != null and ruleDescription != ''">rule_description =#{ruleDescription},</if>
            <if test="taskStatus != null and taskStatus != ''">task_status = #{taskStatus},</if>
            <if test="updateBy !=null">update_by = #{updateBy},</if>
            <if test="updateTime !=null">update_time = #{updateTime},</if>
        </trim>
        where project_rule_id =#{projectRuleId}
    </update>

    <delete id="deleteLoanDateDetail">
        delete from business_control_loandate_detail where project_rule_id = #{projectRuleId}
    </delete>

    <select id="getOldProjectInfo" resultType="java.util.Map">
        select a.project_id as projectId, b.project_name as projectName from project_company_relevance a left join oa_project_deploy b on a.project_id = b.id
        where b.is_enable ='Y'
        <if test="companyId != null">
           and a.unit_id =#{companyId}
        </if>
        <if test="projectIdList !=null">
            and b.id in
            <foreach collection="projectIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryProductNo" resultType="com.ruoyi.nocode.domain.busCont.OldProjectLoanInfo">
        SELECT
        sc.id as companyId,
        sc.company_short_name,
        a.id as projectId,
        a.project_name,
        b.product_no,
        b.product_name
        FROM
        oa_project_deploy a
        left JOIN project_company_relevance r on r.project_id = a.id
        left JOIN d_project_parameter b ON a.id = b.project_id
        left JOIN sys_company sc on r.unit_id = sc.id
        WHERE 1=1
        <if test="companyId != null">
            AND r.unit_id = #{companyId}
        </if>
        <if test="projectIdList != null and projectIdList.size() > 0">
            AND (
            <foreach collection="projectIdList" item="item" index="index">
                <if test="(index % 500) == 0">
                    <if test="index != 0"> OR </if>
                    a.id IN (
                </if>
                #{item}
                <if test="((index + 1) % 500) == 0 or (index + 1) == projectIdList.size()">)
                </if>
                <if test="(index + 1) % 500 != 0 and (index + 1) != projectIdList.size()">,</if>
            </foreach>
            )
        </if>
        order by projectId
    </select>

    <select id="queryProductNoLoanInfo" resultType="com.ruoyi.nocode.domain.busCont.OldProjectLoanInfo">
        select total_amount as loanTotalAmount ,total_plan_balance_amt as loanTotalPlanBalanceAmt from  d_data where product_no = #{productNo} order by recon_date desc limit 1
    </select>

    <insert id="insertControlRuleProcessRecord" parameterType="com.ruoyi.nocode.domain.busCont.ControlRuleProcessRecord">
        insert into control_rule_process_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyType != null and applyType != ''">apply_type,</if>
            <if test="processId != null and processId != ''">process_id,</if>
            <if test="applyIds != null">apply_id_list,</if>
            <if test="operation != null and operation != ''">operation,</if>
            <if test="oaApplyRecordsOldData != null and oaApplyRecordsOldData != ''">oa_apply_records_old_data,</if>
            <if test="oaApplyRecordsNewData != null and oaApplyRecordsNewData != ''">oa_apply_records_new_data,</if>
            <if test="editUserId != null">edit_user_id,</if>
            <if test="editTime != null">edit_time,</if>
            <if test="editInfo != null">edit_info,</if>
            <if test="checkUserId != null">check_user_id,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="checkStatus != null and checkStatus != ''">check_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyType != null and applyType != ''">#{applyType},</if>
            <if test="processId != null and processId != ''">#{processId},</if>
            <if test="applyIds != null">#{applyIds},</if>
            <if test="operation != null and operation != ''">#{operation},</if>
            <if test="oaApplyRecordsOldData != null and oaApplyRecordsOldData != ''">#{oaApplyRecordsOldData},</if>
            <if test="oaApplyRecordsNewData != null and oaApplyRecordsNewData != ''">#{oaApplyRecordsNewData},</if>
            <if test="editUserId != null">#{editUserId},</if>
            <if test="editTime != null">#{editTime},</if>
            <if test="editInfo != null">#{editInfo},</if>
            <if test="checkUserId != null">#{checkUserId},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="checkStatus != null and checkStatus != ''">#{checkStatus},</if>
        </trim>
    </insert>

    <select id="queryBusinessDataRecord" resultMap="BusinessDataRecordResult">
        select id,
               apply_type,
               process_id,
               apply_id_list,
               operation,
               oa_apply_records_old_data,
               oa_apply_records_new_data,
               edit_user_id,
               edit_time,
               edit_info,
               check_user_id,
               check_time,
               check_status
        from control_rule_process_record
        where process_id = #{processId}
    </select>

    <update id="updateControlRuleProcessRecord" parameterType="com.ruoyi.nocode.domain.busCont.ControlRuleProcessRecord">
        update control_rule_process_record set check_status = #{checkStatus},check_time = #{checkTime}
        where 1=1
        <if test="id != null">
          and  id =#{id}
        </if>
        <if test="processId !=null and processId !=''">
          and process_id =#{processId}
        </if>
    </update>

    <delete id="deleteControlProjectRuleById">
        delete from business_control_project_rule where project_rule_id = #{projectRuleId}
    </delete>

    <delete id="deleteProjectDetails">
    delete from business_control_project_detail where project_id = #{projectId}
    </delete>

    <select id="queryBusinessMarginEditRecord" resultType="com.ruoyi.nocode.domain.busCont.BusinessMarginEditRecord">
        select margin_id, project_id, margin_amount, b.nick_name as update_by, a.update_time
        from business_marginedit_record a
         left join sys_user b on a.update_by = b.user_name
        where project_id = #{projectId}
        order by margin_id desc
    </select>

    <insert id="insertBusinessMarginEditRecord" parameterType="com.ruoyi.nocode.domain.busCont.BusinessMarginEditRecord">
        insert into business_marginedit_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="marginId != null">margin_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="marginAmount != null">margin_amount,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="marginId != null">#{marginId},</if>
                <if test="projectId != null">#{projectId},</if>
                <if test="marginAmount != null">#{marginAmount},</if>
                <if test="updateBy != null">#{updateBy},</if>
                <if test="updateTime != null">#{updateTime},</if>
            </trim>
    </insert>
</mapper>