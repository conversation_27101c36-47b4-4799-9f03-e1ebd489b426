-- 中信银行余额对账单表
CREATE TABLE yqzl_citic_balance_statement
(
    bill_no      VARCHAR(33)  NOT NULL COMMENT '余额对账单编号',
    cr_term_no   VARCHAR(6)   NOT NULL COMMENT '账期 格式：YYYYMM',
    cst_no       VARCHAR(12)  NOT NULL COMMENT '三代客户号',
    cst_nm       VARCHAR(363) NOT NULL COMMENT '客户名称',
    inst_num     VARCHAR(28)  NOT NULL COMMENT '机构号',
    inst_nm      VARCHAR(28)  NOT NULL COMMENT '机构名称',
    rule_no      TINYINT      NULL COMMENT '规则序号(0-99)',
    rule_alias   VARCHAR(100) NULL COMMENT '规则别称',
    is_sign      VARCHAR(1)   NULL COMMENT '是否可回签 0/1',
    bill_status  VARCHAR(1)   NULL COMMENT '账单状态 0:全部 1:已回签 2:未回签',
    input_opr_nm VARCHAR(30)  NULL COMMENT '回签人名称',
    bill_mode    VARCHAR(1)   NULL COMMENT '对账单生成模式 0:非集中对账 1:集中对账',
    PRIMARY KEY (bill_no)
) COMMENT = '中信银行余额对账单';


-- 中信银行余额对账单明细表（按 XML 报文字段建表）
# drop table yqzl_citic_balance_statement_detail;
CREATE TABLE yqzl_citic_balance_statement_detail
(
    account_no   VARCHAR(19)    NOT NULL COMMENT '账号',
    acc_seq      VARCHAR(6)     NULL COMMENT '账户序号',
    disaccount   VARCHAR(25)    NULL COMMENT '展示账号（一户通或跨境账户）',
    acc_type     VARCHAR(2)     NULL COMMENT '账户类型 01活期 02活期保证金 03定期 04定期保证金 05通知 06表内资产 07保函 08银承',
    acc_type_nm  VARCHAR(122)   NULL COMMENT '账户类型中文',
    balance      DECIMAL(15, 2) NULL COMMENT '账户余额',
    cry_type     VARCHAR(3)     NULL COMMENT '币种',
    cry_type_nm  VARCHAR(30)    NULL COMMENT '币种中文',
    is_sign      VARCHAR(1)     NULL COMMENT '是否可回签 0可回签 1不可回签',
    open_bank_nm VARCHAR(122)   NULL COMMENT '开户行名称',
    open_bank_no VARCHAR(4)     NULL COMMENT '开户行号',
    review_no    VARCHAR(20)    NULL COMMENT '回签编号（未提交回签不符信息为空）',
    sign_result  VARCHAR(1)     NULL COMMENT '对账回签结果 3回签相符 4回签不符'
) COMMENT = '中信银行余额对账单明细';

-- 中信银行账户余额查询结果表
CREATE TABLE yqzl_citic_account_balance_row
(
    id             bigint auto_increment comment '主键' primary key,
    status         VARCHAR(7)     NULL COMMENT '账户状态',
    status_text    VARCHAR(254)   NULL COMMENT '账户状态信息',
    account_no     VARCHAR(19)    NOT NULL COMMENT '账号',
    account_name   VARCHAR(122)   NULL COMMENT '账户名称',
    currency_id    VARCHAR(2)     NULL COMMENT '币种',
    open_bank_name VARCHAR(122)   NULL COMMENT '开户行名称',
    last_tran_date VARCHAR(8)     NULL COMMENT '最近交易日 YYYYMMDD',
    usable_balance DECIMAL(15, 2) NULL COMMENT '可用账户余额',
    balance        DECIMAL(15, 2) NULL COMMENT '账号余额',
    forzen_amt     DECIMAL(15, 2) NULL COMMENT '冻结（或看管）金额',
    frozen_flag    VARCHAR(1)     NULL COMMENT '账号状态 A:正常 D:睡眠 F:冻结',
    account_type   VARCHAR(2)     NULL COMMENT '账户类型 ST:活期储蓄 IM:活期支票',
    lawpt_lmt      DECIMAL(15, 2) NULL COMMENT '法透额度',
    query_date     DATE           NULL COMMENT '查询日期 YYYY-MM-DD'
) COMMENT = '中信银行账户余额查询结果';



CREATE TABLE yqzl_citic_account_transaction
(
    id                 bigint auto_increment comment '主键' primary key,
    tran_date          VARCHAR(8)     NULL COMMENT '交易日期 YYYYMMDD',
    tran_time          VARCHAR(6)     NULL COMMENT '交易时间 HHMMSS',
    tran_no            VARCHAR(14)    NULL COMMENT '柜员交易号',
    sum_tran_no        VARCHAR(13)    NULL COMMENT '总交易流水号',
    tran_amount        DECIMAL(15, 2) NULL COMMENT '交易金额',
    credit_debit_flag  VARCHAR(1)     NULL COMMENT '借贷标识 D借 C贷',
    opp_account_no     VARCHAR(32)    NULL COMMENT '对方账号',
    opp_account_name   VARCHAR(122)   NULL COMMENT '对方账户名称',
    opp_open_bank_name VARCHAR(122)   NULL COMMENT '对方开户行名',
    abstract_field     VARCHAR(102)   NULL COMMENT '附言',
    cash_transfer_flag VARCHAR(1)     NULL COMMENT '现转标识 0现金 1转账',
    op_id              VARCHAR(20)    NULL COMMENT '网银制单员',
    op_name            VARCHAR(20)    NULL COMMENT '制单员姓名',
    ck_id              VARCHAR(20)    NULL COMMENT '网银审核员',
    ck_name            VARCHAR(20)    NULL COMMENT '审核员姓名',
    balance            DECIMAL(15, 2) NULL COMMENT '账户余额',
    value_date         VARCHAR(8)     NULL COMMENT '起息日期 YYYYMMDD',
    host_tran_code     VARCHAR(7)     NULL COMMENT '主机交易码',
    e3rt_date          VARCHAR(8)     NULL COMMENT '退汇日期 YYYYMMDD',
    e3rt_flag          VARCHAR(1)     NULL COMMENT '退汇标志 0退汇 1非退汇',
    ori_debit_amt      DECIMAL(15, 2) NULL COMMENT '付款原有金额（信银国际）',
    ori_debit_cry      VARCHAR(2)     NULL COMMENT '付款原有币种（信银国际）',
    ori_credit_amt     DECIMAL(15, 2) NULL COMMENT '收款原有金额（信银国际）',
    ori_credit_cry     VARCHAR(2)     NULL COMMENT '收款原有币种（信银国际）',
    tra_cry_type       VARCHAR(2)     NULL COMMENT '交易币种（信银国际）',
    tran_ref_no        VARCHAR(35)    NULL COMMENT '信银国际交易参考号',
    client_id          VARCHAR(20)    NULL COMMENT '客户流水号（信银国际）',
    chk_num            VARCHAR(20)    NULL COMMENT '对账编号',
    rl_tran_no         VARCHAR(14)    NULL COMMENT '关联交易日志号',
    rf_tran_dt         VARCHAR(8)     NULL COMMENT '冲账对方交易日期 YYYYMMDD',
    rf_tran_no         VARCHAR(14)    NULL COMMENT '冲账对方柜员交易号',
    sub_accc_no        VARCHAR(19)    NULL COMMENT '附属账户',
    host_tran_desc     VARCHAR(20)    NULL COMMENT '摘要内容',
    ori_num            VARCHAR(36)    NULL COMMENT '原始流水号'
) COMMENT = '中信银行账户明细信息查询表';

alter table yqzl_citic_account_transaction
    add account_no varchar(32) null comment '交易账号';

alter table yqzl_citic_account_transaction
    add open_bank_name varchar(122) null comment '交易账号开户网点名称 ';


CREATE TABLE yqzl_citic_receipt_detail
(
    id             bigint auto_increment comment '主键' primary key,
    brseq_no       VARCHAR(100)   NULL COMMENT '回单编号',
    bill_type      VARCHAR(6)     NULL COMMENT '回单类型：100000存款 100001取款 200000转账 200001缴税 300000收费 400000定期 400001活期',
    transeq_no     VARCHAR(36)    NULL COMMENT '交易发起方流水号',
    tran_code      VARCHAR(8)     NULL COMMENT '交易发起方交易码（T+1返回）',
    tran_date      VARCHAR(8)     NULL COMMENT '交易日期 YYYYMMDD',
    tran_amt       DECIMAL(15, 2) NULL COMMENT '交易金额',
    payer_acc_no   VARCHAR(19)    NULL COMMENT '付款人账号（T+1返回）',
    payer_acc_nm   VARCHAR(122)   NULL COMMENT '付款人户名（T+1返回）',
    payee_acc_no   VARCHAR(19)    NULL COMMENT '收款人账号（T+1返回）',
    payee_acc_nm   VARCHAR(122)   NULL COMMENT '收款人户名（T+1返回）',
    cry_type       VARCHAR(3)     NULL COMMENT '币种（T+1返回）',
    biz_type       VARCHAR(6)     NULL COMMENT '业务类型（T+1返回）',
    biz_tpdes      VARCHAR(32)    NULL COMMENT '业务类型描述（T+1返回）',
    br_stt         VARCHAR(1)     NULL COMMENT '回单状态（T+0返回） 1正常 2冲正 3被冲正 4当日冲正',
    other_racc_no1 VARCHAR(19)    NULL COMMENT '对手方账号（T+0返回）',
    other_racc_nm1 VARCHAR(122)   NULL COMMENT '对手方账户名（T+0返回）',
    other_racc_no2 VARCHAR(19)    NULL COMMENT '对手方资金分簿（T+0返回）',
    other_racc_nm2 VARCHAR(122)   NULL COMMENT '对手方资金分簿名（T+0返回）',
    cdfg           VARCHAR(1)     NULL COMMENT '借贷标识（T+0返回） C贷/收 D借/付',
    acc_no1        VARCHAR(19)    NULL COMMENT '己方账号(T+0)',
    acc_nm1        VARCHAR(122)   NULL COMMENT '己方账户名(T+0)',
    acc_no2        VARCHAR(19)    NULL COMMENT '己方资金分簿编号(T+0)',
    acc_nm2        VARCHAR(122)   NULL COMMENT '己方资金分簿名(T+0)',
    acc_no3        VARCHAR(19)    NULL COMMENT '己方客户内部账号(T+0)',
    acc_nm3        VARCHAR(122)   NULL COMMENT '己方客户内部账户名(T+0)',
    other_racc_no3 VARCHAR(19)    NULL COMMENT '对方客户内部账号(T+0)',
    other_racc_nm3 VARCHAR(122)   NULL COMMENT '对方客户内部账户名(T+0)',
    cry_type1      VARCHAR(3)     NULL COMMENT '交易币种(T+0)',
    cash_flag1     VARCHAR(1)     NULL COMMENT '钞汇标识(T+0)',
    acc_seq1       VARCHAR(6)     NULL COMMENT '账户序号(T+0)',
    cry_type2      VARCHAR(3)     NULL COMMENT '对手方交易币种(T+0)',
    cash_flag2     VARCHAR(1)     NULL COMMENT '对手方钞汇标识(T+0)',
    acc_seq2       VARCHAR(6)     NULL COMMENT '对手方账户序号(T+0)'
) COMMENT = '中信银行回单信息（T+0/T+1）';


CREATE TABLE yqzl_citic_transfer_order
(
    id                 bigint auto_increment comment '主键' primary key,
    client_id          VARCHAR(20)                        NOT NULL COMMENT '客户流水号',
    pre_flg            VARCHAR(1)                         NOT NULL COMMENT '预约支付标志 0非预约 1预约 2次日',
    pre_date           VARCHAR(8)                         NULL COMMENT '延期支付日期 YYYYMMDD',
    pre_time           VARCHAR(6)                         NULL COMMENT '延期支付时间 hhmmss',
    pay_type           VARCHAR(1)                         NOT NULL COMMENT '支付方式 1跨行 2行内 3企业内部',
    pay_flg            VARCHAR(1)                         NOT NULL COMMENT '支付时效 0加急 1普通',
    pay_account_no     VARCHAR(19)                        NOT NULL COMMENT '付款账号',
    rec_account_no     VARCHAR(32)                        NOT NULL COMMENT '收款账号',
    rec_account_name   VARCHAR(122)                       NOT NULL COMMENT '收款账号名称',
    rec_open_bank_name VARCHAR(122)                       NULL COMMENT '收款开户行名',
    rec_open_bank_code VARCHAR(20)                        NULL COMMENT '收款开户行网点号',
    tran_amount        DECIMAL(15, 2)                     NOT NULL COMMENT '金额',
    abstract_field     VARCHAR(102)                       NOT NULL COMMENT '附言',
    memo               VARCHAR(60)                        NULL COMMENT '备注',
    chk_num            VARCHAR(20)                        NULL COMMENT '对账编号',
    handled_by         bigint                             NULL COMMENT '经办人',
    reviewer           bigint                             NULL COMMENT '审核人',
    state              VARCHAR(2)                         NULL COMMENT '状态 1.未提交、2.审核中、3.审核通过、4.交易中、5.等待交易、6.交易成功、7.交易失败',

    create_by          varchar(64)                        null comment '创建者',
    create_time        datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by          varchar(64)                        null comment '更新者',
    update_time        datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
) COMMENT ='中信银行支付转账表';


alter table yqzl_citic_transfer_order
    add process_id varchar(50) null comment '流程id' after state;

alter table yqzl_citic_transfer_order
    add zindex varchar(10) null comment '流程第几个转账' after process_id;

alter table yqzl_citic_transfer_order
    add status_text varchar(300) null comment '交易状态信息' after zindex;

alter table yqzl_citic_transfer_order
    add review_comment varchar(100) null comment '审核意见' after status_text;

alter table yqzl_citic_transfer_order
    add review_time datetime null comment '审批时间' after review_comment;


create table yqzl_notify
(
    id               bigint auto_increment comment '主键'
        primary key,
    notify_module    varchar(50)                        null comment '通知模块',
    url              varchar(50)                        null comment '相关url',
    notify_type      char                               null comment '通知类型 0通知 1待办',
    notify_msg       varchar(500)                       null comment '通知内容',
    dispose_user     bigint                             null comment '待处理人id',
    view_flag        char                               null comment '阅读状态 0未阅 1已阅',
    status           char                               null comment '状态 0正常 1禁用',
    remind_text      varchar(600)                       null comment '提醒正文',
    correlation_id   bigint                             null comment '关联id',
    yqzl_notify_type varchar(5)                         null comment '银企直联通知类型(1.支付转账通知)',
#     view_time       datetime                             null comment '查看通知时间',
#     fill_in_time    datetime                             null comment '填写回执时间',
#     receipt_state   varchar(5) default '0'               null comment '回执状态 (0.未填写回执 1.参加 2.不参加)',
#     receipt_content varchar(500)                         null comment '回执留言',
#     is_vx_notify    varchar(2) default '0'               null comment '是否已发送微信提醒(0.发送 1.已发送)',
    create_by        varchar(64)                        null comment '创建者',
    create_time      datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by        varchar(64)                        null comment '更新人',
    update_time      datetime                           null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '银企直联相关提醒表';

alter table yqzl_notify
    alter column view_flag set default '0';
alter table yqzl_notify
    alter column status set default '0';


create table yqzl_account_manager
(
    id             bigint auto_increment comment '主键' primary key,
    manager_id     bigint                             null comment '用户id',
    bank_account   varchar(64)                        null comment '银行账户',
    open_bank      varchar(64)                        null comment '开户银行名称',
    bank_name      varchar(64)                        null comment '开户银行名称',
    reminder_cycle varchar(10)                        null comment '回单提醒周期',
    cycle_days     int                                null comment '天数提醒',
    calculate_time datetime                           null comment '防久悬初始计算日期',
    calculate_days int                                null comment '防久悬提醒周期',
    create_by      varchar(64)                        null comment '创建者',
    create_time    datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by      varchar(64)                        null comment '更新人',
    update_time    datetime                           null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '账户管理人设置';

create table yqzl_payment_password
(
    id             bigint auto_increment comment '主键' primary key,
    manager_id     bigint                             null comment '用户id',
    password       varchar(100)                        null comment '支付密码',
    create_by      varchar(64)                        null comment '创建者',
    create_time    datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by      varchar(64)                        null comment '更新人',
    update_time    datetime                           null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '银企直联支付密码';

ALTER TABLE yqzl_payment_password ADD UNIQUE INDEX idx_manager_id (manager_id);


alter table yqzl_citic_balance_statement_detail
    add bill_no varchar(50) null comment '余额对账单编号';


alter table yqzl_citic_receipt_detail
    add qry_type varchar(5) null comment '查询类型 1：T+0 2：T+1';

alter table yqzl_citic_receipt_detail
    add preview_sign varchar(5) default '0' null comment '预览标识(0.未预览 1. 已预览)';

alter table yqzl_citic_receipt_detail
    add download_sign varchar(5) default '0' null comment '下载标识(0.未下载 1. 已下载)';

alter table yqzl_citic_receipt_detail
    add download_count int default 0 null comment '下载次数';



create table yqzl_abstract_field
(
    id             bigint auto_increment comment '主键' primary key,
    user_id     bigint                             null comment '用户id',
    abstract_field       varchar(100)                        null comment '附言',
    create_by      varchar(64)                        null comment '创建者',
    create_time    datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by      varchar(64)                        null comment '更新人',
    update_time    datetime                           null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '银企直联附言';


alter table yqzl_citic_transfer_order
    add rec_account_number varchar(100) null comment '账户编号  ' after rec_account_name;


create table yqzl_receiving_account
(
    id             bigint auto_increment comment '主键' primary key,
    user_id     bigint                             null comment '用户id',
    rec_account_no     varchar(100)                        null comment '账户',
    rec_account_name   varchar(100)                        null comment '账户名称',
    rec_account_number       varchar(100)                        null comment '账户编号',
    rec_open_bank_name varchar(100)                        null comment '收款开户行名',
    rec_open_bank_code varchar(100)                        null comment '收款开户行网点号',
    create_by      varchar(64)                        null comment '创建者',
    create_time    datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by      varchar(64)                        null comment '更新人',
    update_time    datetime                           null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '银企收款账号';

alter table yqzl_account_manager
    modify bank_name varchar(64) null comment '账户名称';
