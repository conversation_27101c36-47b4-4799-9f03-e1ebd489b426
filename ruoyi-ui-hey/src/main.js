import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import api from './api/financial';

import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import AppContent from '@/components/Financial/AppContent';
import AccountDateChoose from '@/components/Financial/AccountDateChoose'
import SubMenu from '@/components/Financial/common/sub-menu';
import Chart from '@/views/financial/app/chart/echarts';

import './assets/financial/js/config/dict';
import './assets/financial/js/common/filters';
import { format } from '@/utils/ruoyi.js'
Vue.prototype.$format = format
if (process.env.NODE_ENV !== 'development') {
  require('./reporter');
}
import directive from './directive' // directive
Vue.use(directive)
require('font-awesome/css/font-awesome.css');
require('./assets/financial/styles/app.less');

Vue.use(HeyUI);
Vue.use(ElementUI);
Vue.prototype.$api = api;

Vue.component("app-content", AppContent);
Vue.component("account-date-choose", AccountDateChoose);
Vue.component('sub-menu', SubMenu);
Vue.component('Chart', Chart);


Vue.config.productionTip = false

const VueConfig = {
  router,
  store,
};

store.dispatch("init").then(() => {
  store.dispatch("GetInfo");
  //实例化界面
  new Vue(Object.assign(VueConfig, {
    render: h => h(App)
  })).$mount('#app');
}).catch(() => {
  new Vue(Object.assign(VueConfig, {
    render: h => h(Login)
  })).$mount('#app');
});


