.h-panel {
  border: none;

  &-title {
    color: @dark-color;
  }

  &-bar {
    padding: 10px @margin;
  }

  &-tabs-bar {
    .h-tabs-default > .h-tabs-item {
      padding: 16px 15px;
      font-size: 18px;
    }
  }

  &-bar-s {
    padding-top: 8px;
    padding-bottom: 8px;

    .h-panel-title {
      font-size: 15px;
    }
  }

  &-body {
    padding: @margin;
  }
}

.label.h-col {
  line-height: 32px;
}

.h-menu-white .h-menu-li .h-menu-li-selected {
  background-color: #f0f6ff;
}

.h-table {
  td, th {
    font-size: 15px;
  }

  th {
    height: 42px;
  }

  td {
    height: 42px;
  }
}

.h-tree-li {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.nowrap {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}