/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : 字典</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月02日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
HeyUI.initDict({
	accountingStandards: {0: "小企业会计准则", 1: "企业会计准则", 2: "科技会计准则", 3: "担保会计准则"},
	defaultRadios: {1: "是", 0: "否"},
	statusRadios: {1: "正常", 0: "不可用"},
	enableRadios: {1: "启用", 0: "不启用"},
	needRadios: {1: "需要", 0: "不需要"},
	reportTemplateType: {0: "普通报表", 1: "资产报表"},
	reportTemplateItemType: {0: "资产", 1: "负债", 2: "所有者权益"},
	accessRules0: {0: " 净发生额", 1: "借方发生额", 2: "贷方发生额"},
	accessRules1: {3: "余额", 4: "借方余额", 5: "贷方余额"},
	operation: {'+': " +", '-': "-"},
	reportTemplateItemSources: {0: "表外公式", 1: "表内公式"},
	vatRadios: {0: "小规模纳税人", 1: "一般纳税人"},
	roles: {'Manager': "账套管理员", 'Director': "主管", 'Making': "制单人", 'Cashier': "出纳", 'View': "查看"},
	voucherTemplateType: {0: "日常支出", 1: "采购", 2: "销售", 3: "工资", 4: "税金", 5: "折旧和摊销"},
	industry: {"0": "IT·通信·电子·互联网", "1": "金融业", "2": "房地产·建筑业", "3": "商业服务", "4": "贸易·批发·零售·租赁业", "5": "文体教育·工艺美术", "6": "生产·加工·制造", "7": "交通·运输·物流·仓储", "8": "服务业", "9": "文化·传媒·娱乐·体育", "10": "能源·矿产·环保", "11": "政府·非盈利机构", "12": "农·林·牧·渔·其他"}
});
