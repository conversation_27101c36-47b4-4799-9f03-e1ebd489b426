<template>
	<app-content class="h-panel">
<!--    <div class="h-input h-input-prefix-icon" style="float: right">
      <input type="text" placeholder="输入账套名称" v-model="accountSetsName" @keyup.enter="searchAccountSets"/>
      <i class="h-icon-search"></i>
    </div>
    <Tabs :datas="accountSetsList" v-model="accountsSetsSelected" @click="accountsSetsChange"></Tabs>-->

    <div style="height: 10px"></div>
<!--    <Form v-width="450" :labelWidth="60" >
      <FormItem label="账套:" prop="accountSetsId">
        <Row type="flex">
          <Cell width="20" style="margin-right: 5px">
            <Select v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="accountsSetsChange"></Select>
          </Cell>
        </Row>
      </FormItem>
    </Form>-->

		<Tabs :datas="datas" v-model="selected" @click="tabChange"></Tabs>
		<router-view/>
	</app-content>
</template>

<script>
	export default {
		name: "check-out",
		data() {
			return {
				datas: [{
					title: '期末结转',
					key: 'CheckList'
				}, {
					title: '反结账',
					key: 'UnCheckOut'
				}],
        selected: "CheckList",
        //账套选中标识
        accountsSetsSelected: null,
        accountSetsList: [],
        accountSetsName: "",
			}
		},
    computed: {
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      },
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      }
    },
    mounted() {
      //账套列表
      this.pushDatas();
    },
    updated() {
      this.selected = this.$route.name
    },
    methods: {
			tabChange(data) {
        this.$router.push({name: data.key})
			},
      accountsSetsChange(data) {
        //查询
        this.$store.dispatch('init', data.key).then(() => {
          if(this.selected=='CheckList'){
            this.$router.push({name: "UnCheckOut"})
          }else{
            this.$router.push({name: "CheckList"})
          }
        })
      },
      pushDatas(){
        this.accountSetsList = [];
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountsSetsSelected = this.currentAccountSets.id;
      },
      searchAccountSets(event){
        if(this.accountSetsName != ''){
          let accountSetsMap = {};
          this.myAccountSets.forEach(accountSets => {accountSetsMap[accountSets.companyName] =  accountSets.id})
          //查询
          this.$store.dispatch('init', accountSetsMap[this.accountSetsName]).then(() => {
            this.accountSetsList = [];
            let accountSet = {
              title: this.currentAccountSets.companyName,
              key: this.currentAccountSets.id
            }
            this.accountSetsList.push(accountSet)
          })

        }else{
          this.pushDatas()
        }
      },
		}
	}
</script>
