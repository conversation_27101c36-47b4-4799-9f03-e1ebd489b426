<template>
  <div style="padding: 16px">
    <div style="padding: 20px; background: #fff">
      <div class="search">
        <div class="item">
          <span>智慧财务系统客商</span>
          <Select
            style="width: 250px"
            v-model="params.merchantName"
            placeholder="请输入/选择客商名称"
            :datas="merchantNames"
            keyName="merchantName"
            titleName="merchantName"
            filterable
          />
        </div>
        <div class="item">
          <span>用友U8C系统客商</span>
          <Select
            style="width: 250px"
            v-model="params.u8cMerchantName"
            placeholder="请输入/选择客商名称"
            :datas="u8cmerchantNames"
            keyName="u8cMerchantName"
            titleName="u8cMerchantName"
            filterable
          />
        </div>
        <div class="item">
          <span>用友U8C客商类型</span>
          <Select
            style="width: 250px"
            v-model="params.u8cInteriorType"
            placeholder="请选择客商类型"
            keyName="dictValue"
            titleName="dictLabel"
            :datas="patronTypes"
            filterable
          />
        </div>
        <div class="item">
          <span>关联方式</span>
          <Select
            style="width: 250px"
            v-model="params.relevanceType"
            placeholder="请选择关联方式"
            keyName="dictValue"
            titleName="dictLabel"
            :datas="relevanceTypes"
            filterable
          />
        </div>
        <Button
          style="height: 32px; margin-right: 12px"
          @click="getList"
          color="blue"
          icon="h-icon-search"
          >搜索</Button
        >
        <Button style="height: 32px" icon="h-icon-refresh" @click="reset"
          >重置</Button
        >
      </div>
      <div class="solid"></div>
      <el-table :data="datas" style="width: 100%; margin-top: 12px" border="">
        <el-table-column
          align="left"
          label="用友U8C系统客商"
          prop="u8cMerchantName"
        ></el-table-column>
        <el-table-column
          align="left"
          label="用友U8C客商类型"
          prop="u8cMerchantName"
        >
          <template slot-scope="scope">
            {{ patronTypes[scope.row.u8cInteriorType].dictLabel || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="智慧财务系统客商"
          prop="merchantName"
        >
        </el-table-column>
        <el-table-column align="left" label="关联方式" prop="u8cMerchantName">
          <template slot-scope="scope">
            {{
              scope.row.relevanceType
                ? relevanceTypes[scope.row.relevanceType].dictLabel
                : "-"
            }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="最近关联时间" prop="createTime">
        </el-table-column>
        <el-table-column align="left" label="操作" prop="">
          <template slot-scope="scope">
            <Button
              v-if="
                permissions.includes('withCustomer:edit') ||
                permissions.includes('*:*:*')
              "
              noBorder
              text-color="blue"
              @click="edit(scope.row)"
              >修改</Button
            >
            <Button noBorder text-color="blue" @click="record(scope.row)"
              >查看修改记录</Button
            >
          </template>
        </el-table-column>
      </el-table>

      <Pagination
        style="margin-top: 20px"
        v-model="pagination"
        @change="getList"
        layout="total,sizes,pager,jumper"
        align="center"
      />
    </div>
    <CustomerEdit
      :itemData="itemData"
      :types="{ patronTypes, relevanceTypes }"
      v-if="CustomerEditType"
      @close="close"
    />
    <CustomerRecord
      v-if="CustomerRecordType"
      @close="close"
      :id="itemData.u8cMerchantId"
    />
  </div>
</template>

<script>
import CustomerRecord from "../../../components/u8c/CustomerRecord.vue";
import CustomerEdit from "../../../components/u8c/CustomerEdit.vue";

export default {
  name: "withCustomer",
  components: {
    CustomerEdit,
    CustomerRecord,
  },
  data() {
    return {
      CustomerRecordType: false,
      itemData: null,
      CustomerEditType: false,
      u8cmerchantNames: [],
      merchantNames: [],
      patronTypes: [],
      relevanceTypes: [],
      datas: [],
      params: {
        merchantName: "",
        u8cMerchantName: "",
        u8cInteriorType: "",
        relevanceType: "",
      },
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
    };
  },
  computed: {
    permissions() {
      return this.$store.getters.permissions;
    },
  },
  created() {
    this.getDatas();
  },
  mounted() {
    this.$api.u8c.list({}).then((res) => {
      this.u8cmerchantNames = res.rows;
      const str = "merchantId";
      this.merchantNames = this.u8cmerchantNames.filter((item) => str in item);
    });

    this.getList();
  },
  methods: {
    close() {
      this.CustomerEditType = false;
      this.CustomerRecordType = false;
      this.getList();
    },
    record(v) {
      this.itemData = v;
      this.CustomerRecordType = true;
    },
    edit(v) {
      console.log(v);
      this.itemData = v;
      this.CustomerEditType = true;
    },
    reset() {
      this.pagination = {
        page: 1,
        size: 10,
        total: 0,
      };
      this.params = {
        merchantName: "",
        u8cMerchantName: "",
        u8cInteriorType: "",
        relevanceType: "",
      };
      this.getList();
    },
    getList() {
      let data = {
        pageSize: this.pagination.size,
        pageNum: this.pagination.page,
        ...this.params,
      };
      this.$api.u8c.list(data).then((res) => {
        this.datas = res.rows;
        console.log(this.datas,'--=-');
        
        this.pagination.total = res.total;
      });
    },
    getDatas() {
      this.$api.u8c.getDicts("u8c_patron_type").then((res) => {
        this.patronTypes = res.data;
      });
      this.$api.u8c.getDicts("u8c_relevance_type").then((res) => {
        this.relevanceTypes = res.data;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  display: flex;
  flex-wrap: wrap;
  .item {
    margin-right: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    span {
      margin-right: 5px;
      font-weight: bold;
    }
  }
}
.solid {
  width: 100%;
  height: 1px;
  background: #f0f0f0;
}
</style>