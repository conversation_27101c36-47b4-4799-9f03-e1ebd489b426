<template>
  <div style="padding: 16px">
    <div style="background: #fff; padding: 20px">
      <div class="search">
        <div class="item">
          <span>账套</span>
          <Select style="width: 250px" v-model="accountsSetsSelected" @change="changeAcc" :datas="accountSetsList"
            filterable :deletable="false"></Select>
        </div>
      </div>
      <div class="search">
        <div class="item">
          <span>摘要</span>
          <input type="text" style="width: 250px" v-model="params.summary" placeholder="请输入摘要" />
        </div>
        <div class="item">
          <span>科目名称</span>
          <input type="text" v-model="params.subjectName" style="width: 250px" placeholder="请输入科目名称" />
        </div>
        <div class="item">
          <span>科目编码</span>
          <input type="text" v-model="params.subjectCode" style="width: 250px" placeholder="请输入科目编码" />
        </div>

        <div class="item">
          <span>科目类型</span>
          <Select style="width: 250px" v-model="params.type" placeholder="请选择科目类型" :datas="types" keyName="dictValue"
            titleName="dictLabel" filterable />
        </div>
        <div class="item">
          <span>凭证日期</span>
          <DateRangePicker style="width: 250px" v-model="date" format="YYYY-MM-DD"></DateRangePicker>
        </div>
        <Button style="height: 32px; margin-right: 20px" @click="getList" color="blue" icon="h-icon-search">搜索</Button>
        <Button style="height: 32px" icon="h-icon-refresh" @click="reset">重置</Button>
      </div>
      <div class="solid"></div>
      <div style="margin: 20px 0">
        <Button style="margin-right: 20px" text-color="blue" v-if="
          permissions.includes('voucher:u8cPush') ||
          permissions.includes('*:*:*')
        " :disabled="selectIds.length == 0" @click="send(0)">单张凭证批量推送至用友U8C</Button>
        <Button style="margin-right: 20px" text-color="blue" v-if="
          permissions.includes('voucher:u8cPush') ||
          permissions.includes('*:*:*')
        " :disabled="selectIds.length == 0" @click="send(1)">合并凭证推送至用友U8C
          <el-tooltip class="item" effect="dark" content="请选择借贷科目相同，且借贷笔数相等的凭证进行合并推送" placement="top">
            <i class="el-icon-warning"></i> </el-tooltip></Button>
        <Button style="margin-right: 20px" v-if="
          permissions.includes('voucher:config') ||
          permissions.includes('*:*:*')
        " @click="$router.push('/configVoucher')" color="blue">配置凭证推送规则</Button>
        <Button style="margin-right: 20px" color="blue" @click="SelectListType = true">已选择 ( {{ selectIds.length }} )
          条凭证</Button>
      </div>
      <div class="h-panel-body" style="padding: 0">
        <table class="header">
          <tr>
            <th style="width: 50px">
              <input :checked="checkAll" type="checkbox" @click="checkAll = !checkAll" />
            </th>
            <td style="width: 215px">摘要</td>
            <td>科目</td>
            <td align="right" style="width: 130px">借方金额</td>
            <td align="right" style="width: 130px">贷方金额</td>
          </tr>
        </table>
        <table v-if="!datas.length && accountsSetsSelected">
          <tr>
            <td colspan="5" class="text-center padding">暂无数据</td>
          </tr>
        </table>
        <table v-if="!accountsSetsSelected">
          <tr>
            <td colspan="5" class="text-center padding">请选择账套</td>
          </tr>
        </table>
        <table class="details" v-for="(data, i) in datas" :key="data.id">
          <tr class="details-header">
            <th style="width: 50px">
              <input :class="{ display: data._checked }" v-model="data._checked" type="checkbox"
                @click="getItemCheck(data, i)" />
            </th>
            <td colspan="2">
              日期：{{ data.voucherDate }} 凭证字号：{{ data.word }}-{{
                data.code
              }}
              状态：<span v-if="!data.valid" style="color: red">已作废</span><span v-if="data.valid">{{
                data.auditMemberId ? "已审核" : "待审核"
              }}</span>
              <span v-if="data.mergeCount" style="font-weight: bold; margin-left: 20px">已合并推送{{ data.mergeCount
              }}次</span>
              <span v-if="data.aloneCount" style="font-weight: bold; margin-left: 15px">单独推送{{ data.aloneCount
              }}次</span>
              <span v-if="data.failureCount" style="font-weight: bold; margin-left: 15px; color: red">推送失败{{
                data.failureCount }}次</span>
              <span v-if="
                !data.mergeCount && !data.aloneCount && !data.failureCount
              " style="font-weight: bold; margin-left: 20px">该凭证尚未进行推送</span>
              <el-button type="primary" size="mini" style="margin-left: 20px"
                v-if="data.mergeCount || data.aloneCount || data.failureCount"
                @click="seeDetail(data)">查看推送记录</el-button>
            </td>
            <td colspan="2" class="actions" align="right">
              <router-link tag="span" :to="{
                name: 'VoucherForm',
                params: { voucherId: data.id, showDetail: true },
              }">查看</router-link>
            </td>
          </tr>
          <tr v-for="d in data.voucherDetails" :key="d.id" :class="{ 'un-valid': !data.valid }">
            <th></th>
            <td style="width: 215px">
              {{ d.summary }}
              <template v-if="d.subject && d.num && d.price">
                (数量:{{ d.num
                }}<span class="dark4-color">{{ d.subject.unit }}</span>，单价:{{ d.price }}<span
                  class="dark4-color">元</span>)
              </template>
            </td>
            <td>{{ d.subjectName }}</td>
            <td align="right" style="width: 130px">
              {{ d.debitAmount | numFormat }}
            </td>
            <td align="right" style="width: 130px">
              {{ d.creditAmount | numFormat }}
            </td>
          </tr>
          <tr class="font-bold" :class="{ 'un-valid': !data.valid }">
            <td></td>
            <td>合计</td>
            <td>{{ data.debitAmount | dxMoney }}</td>
            <td align="right">{{ data.debitAmount | numFormat }}</td>
            <td align="right">{{ data.creditAmount | numFormat }}</td>
          </tr>
        </table>
        <Pagination v-model="pagination" @change="getList" layout="total,sizes,pager,jumper" align="center" />
      </div>
    </div>
    <SelectTable v-if="selectTableType" @close="selectTableType = false" @submit="submitSend"
      :accountId="accountsSetsSelected" />
    <ConfirmSend :ruleId="ruleId" :datas="sendList" v-if="ConfirmSendType" @submit="submitOneSend"
      @alignSelect="alignSelect" @close="ConfirmSendType = false" />
    <ErrorSend @alignSelect="alignSelect" :datas="sendList" v-if="ErrorSendType" @close="ErrorSendType = false" />
    <ErrorDetail :datas="sendList" v-if="ErrorDetailType" @close="ErrorDetailType = false" />
    <MoreSend @alignSelect="alignSelect" :datas="sendList" v-if="MoreSendType" @submit="moreSend"
      @close="MoreSendType = false" />
    <SuccessSend v-if="successSendType" @close="close" :str="str" />
    <SendRecord v-if="SendRecordType" @close="SendRecordType = false" :id="itemData.id" />
    <SelectList v-if="SelectListType" @close="closeSelect" :datas="selectList" />
  </div>
</template>

<script>
import SelectTable from "../../../components/u8c/SelectTable.vue";
import ConfirmSend from "../../../components/u8c/ConfirmSend.vue";
import ErrorSend from "../../../components/u8c/ErrorSend.vue";
import ErrorDetail from "../../../components/u8c/ErrorDetail.vue";
import MoreSend from "../../../components/u8c/MoreSend.vue";
import SuccessSend from "../../../components/u8c/SuccessSend.vue";
import SendRecord from "../../../components/u8c/SendRecord.vue";
import SelectList from "../../../components/u8c/SelectList.vue";

export default {
  name: "U8cPush",
  components: {
    SelectTable,
    ConfirmSend,
    ErrorSend,
    MoreSend,
    ErrorDetail,
    SuccessSend,
    SendRecord,
    SelectList,
  },
  data() {
    return {
      str: "",
      SelectListType: false,
      itemData: null,
      SendRecordType: false,
      successSendType: false,
      ErrorDetailType: false,
      ErrorSendType: false,
      MoreSendType: false,
      ConfirmSendType: false,
      selectTableType: false,
      checkAll: false,
      datas: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },

      //账套选中标识
      accountsSetsSelected: null,
      //审核状态选中标识
      auditStateSelected: "0",
      params: {
        summary: "",
        subjectName: "",
        subjectCode: "",
        type: "",
      },
      select: "",
      value: "",
      date: {},
      selectList: [],
      selectIds: [],
      sendList: [],
      ruleId: null,
      accountSetsList: [],
      types: [],
      sendType: "",
    };
  },
  computed: {
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    },
    permissions() {
      return this.$store.getters.permissions;
    },
  },
  watch: {
    checkAll(nval, oldval) {
      console.log(nval);
      let data = Array.from(this.datas);
      data.forEach((val) => (val._checked = nval));
      this.datas = data;
      if (nval) {
        this.selectList = this.datas;
        this.selectIds = this.datas.map((item) => item.id);
      } else {
        this.selectList = [];
        this.selectIds = [];
      }
      // let checked = this.datas.filter(value => value._checked);
      // console.log(checked);
    },
  },
  mounted() {
    this.$api.u8c.getDicts("u8c_subject_type").then((res) => {
      this.types = res.data;
    });

    this.myAccountSets.forEach((e) => {
      let accountSet = {
        title: e.companyName,
        key: e.id,
      };
      this.accountSetsList.push(accountSet);
    });
    // this.getList();
  },
  methods: {
    close() {
      this.successSendType = false;
      this.SelectListType = false;
      this.SendRecordType = false;
      this.SelectListType = false;
      this.MoreSendType = false;
      this.ConfirmSendType = false;
      this.selectTableType = false;
      this.selectList = [];
      this.selectIds = [];
      this.getList();
    },
    closeSelect(v) {
      console.log(v);
      this.selectList = v;
      this.selectIds = this.selectList.map((item) => item.id);
      this.datas.forEach((item) => {
        if (item._checked && !this.selectIds.includes(item.id)) {
          item._checked = false;
        }
      });
      this.SelectListType = false;
    },
    seeDetail(v) {
      this.itemData = v;
      this.SendRecordType = true;
      // this.$api.u8c.setVoucherDetail(v.id);
    },
    submitOneSend(v) {
      console.log(v);
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.$api.u8c.pushSingleVoucher({ ...v }).then((res) => {
        loading.close();
        this.sendList = res.rows;
        this.ConfirmSendType = false;
        if (
          res.rows.length > 0 &&
          res.rows[0].failureMsg &&
          res.rows[0].failureMsg.length > 0
        ) {
          this.ErrorDetailType = true;
          this.selectIds = [];
          this.selectList = [];
        } else {
          console.log(123);
          this.str = `您本次成功推送【${v.voucherList.length}】条凭证`;
          this.successSendType = true;
          this.selectIds = [];
          this.selectList = [];
        }
        this.getList();
      })
        .catch((err) => {
          loading.close();
          this.$message.error(err.message);
        });
    },
    moreSend(v, e) {
      v.forEach((item) => {
        item.ruleId = this.ruleId;
        item.vouIds = this.selectIds;
        item.voucherCreateTime = e;
      });
      let data = {
        ruleId: this.ruleId,
        voucherCreateTime: e,
        voucherList: v,
        vouIds: this.selectIds,
      };
      console.log(data);
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.$api.u8c.pushSingleVoucherMerge({ ...data }).then((res) => {
        loading.close();
        this.sendList = res.rows;
        this.ConfirmSendType = false;
        if (
          res.rows.length > 0 &&
          res.rows[0].failureMsg &&
          res.rows[0].failureMsg.length > 0
        ) {
          this.ErrorDetailType = true;
          this.selectIds = [];
          this.selectList = [];
        } else {
          this.str = `您本次成功推送【${v.length}】条凭证`;
          this.successSendType = true;
          this.selectIds = [];
          this.selectList = [];
        }
        this.getList();
      })
        .catch((err) => {
          loading.close();
          this.$message.error(err.message);
        });
    },
    changeAcc(v) {
      console.log(v);
      this.selectIds = [];
      this.selectList = [];

      sessionStorage.setItem("account", JSON.stringify(v));
      this.getList();
    },
    submitSend(v) {
      console.log(v);
      this.ruleId = v;
      this.selectList.forEach((item) => {
        item.ruleId = v;

      });
      let data = {
        ruleId: v,
        voucherList: this.selectList,
        aotoFlag: '0'

      };

      if (this.sendType == 0) {
        this.$api.u8c.singlePushU8C({ ...data }).then((res) => {
          this.sendList = res.rows;
          this.selectTableType = false;
          if (
            this.sendList[0].failureMsgList &&
            this.sendList[0].failureMsgList.length > 0
          ) {
            this.ErrorSendType = true;
          } else {
            this.ConfirmSendType = true;
          }
        });
      } else {
        this.$api.u8c.singlePushU8CMerge({ ...data }).then((res) => {
          this.sendList = res.rows;
          this.selectTableType = false;
          if (
            this.sendList[0].failureMsgList &&
            this.sendList[0].failureMsgList.length > 0
          ) {
            this.ErrorSendType = true;
          } else {
            this.MoreSendType = true;
          }
        });
      }
    },
    send(v) {
      this.sendType = v;
      let data = {
        voucherList: this.selectList,
        aotoFlag: '1'

      };

      if (this.sendType == 0) {
        this.$api.u8c.singlePushU8C({ ...data }).then((res) => {
          this.sendList = res.rows;

          if (
            this.sendList[0].failureMsgList &&
            this.sendList[0].failureMsgList.length > 0
          ) {
            this.ErrorSendType = true;
          } else {
            this.ConfirmSendType = true;
          }
        });
      } else {
        this.$api.u8c.singlePushU8CMerge({ ...data }).then((res) => {
          this.sendList = res.rows;

          if (
            this.sendList[0].failureMsgList &&
            this.sendList[0].failureMsgList.length > 0
          ) {
            this.ErrorSendType = true;
          } else {
            this.MoreSendType = true;
          }
        });
      }
      return
      this.selectTableType = true;
    },
    alignSelect() {
      this.ConfirmSendType = false;
      this.MoreSendType = false;
      this.selectTableType = true;

    },
    getItemCheck(v, i) {
      if (v._checked && this.selectIds.includes(v.id)) {
        this.selectIds.forEach((item, index) => {
          if (item == v.id) {
            this.selectIds.splice(index, 1);
          }
        });
        this.selectList.forEach((item, index) => {
          if (item.id == v.id) {
            this.selectList.splice(index, 1);
          }
        });
      } else if (!this.selectIds.includes(v.id)) {
        this.selectList.push(v);
        this.selectIds.push(v.id);
      }
      this.datas[i]._checked = !this.datas[i]._checked;
      console.log(this.selectIds);
    },
    reset() {
      this.date = {};
      this.pagination.page = 1;
      this.pagination.size = 10;
      this.accountsSetsSelected = "";
      this.params = {
        summary: "",
        subjectName: "",
        subjectCode: "",
        type: "",
      };
      this.selectIds = [];
      this.selectList = [];
      this.getList();
    },
    getList() {
      let reqData = {
        startTime: this.date.start,
        endTime: this.date.end,
        pageNum: this.pagination.page,
        pageSize: this.pagination.size,
        ...this.params,
        accountSetsId: this.accountsSetsSelected,
      };

      this.$api.u8c.setVoucherList(reqData).then((res) => {
        this.datas = res.rows;
        this.datas.forEach((item) => {
          this.selectIds.forEach((i) => {
            if (item.id == i) {
              item._checked = true;
            }
          });
        });
        this.pagination.total = res.total;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  display: flex;
  flex-wrap: wrap;

  .item {
    margin-right: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    span {
      margin-right: 5px;
    }
  }
}

.solid {
  width: 100%;
  height: 1px;
  background: #f0f0f0;
}

.h-panel-body {
  table {
    width: 100%;
    border-collapse: collapse;

    td {
      padding: 7px;
    }

    &.header {
      background-color: @primary-color;
      color: white;
    }
  }

  .details {
    font-size: 12px;
    margin: 15px 0;
    border: 1px solid @gray2-color;

    .actions {
      text-align: right;
      padding-right: 20px;

      span,
      a {
        display: none;
      }
    }

    input {
      &.display {
        display: inline-block;
      }
    }

    &-header {
      background-color: @gray3-color;
      color: @dark3-color;
    }

    td,
    th {
      border-bottom: 1px solid @gray2-color;
    }

    tr:hover:not(.details-header) {
      background-color: #dff7df;
      cursor: pointer;
    }

    &:hover {
      box-shadow: 0 0 10px 0 #dadada;
      border-color: #dadada;

      .actions {

        span,
        a {
          display: inline-block;
        }
      }

      input {
        display: inline-block;
      }
    }
  }
}
</style>