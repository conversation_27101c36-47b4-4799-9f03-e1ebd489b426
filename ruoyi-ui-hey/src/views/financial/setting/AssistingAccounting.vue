<template>
	<app-content class="h-panel">
    <div style="height: 10px"></div>
<!--    <Form v-width="300" :labelWidth="80" >
      <FormItem label="账套:" prop="accountSetsId">
        <Select v-model="accountSetsId" :datas="accountSetsList" :null-option="false" @change="accountsSetsChange"></Select>
      </FormItem>
    </Form>-->

		<Tabs :datas="tabDatas" v-model="selected"></Tabs>
		<div class="h-panel-body padding">
			<router-view></router-view>
		</div>
	</app-content>
</template>
<script>
	export default {
		data() {
			return {
				selected: 'AccountingCategory',
				tabDatas: [{
					title: '辅助核算类别',
					key: 'AccountingCategory'
				}],
        accountSetsId: "2",
        accountSetsList: []
			};
		},
		watch: {
			selected(val) {
        if (val == 'AccountingCategory') {
					this.$router.push({name: val, params: {accountSetsId: this.accountSetsId}})
				} else {
					this.$router.push({name: 'CategoryCustom', params: {id: val, accountSetsId: this.accountSetsId}})
				}
			}
		},
		mounted() {
      this.pushDatas();
    },
    computed: {
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      },
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      }
    },
    methods: {
			init() {
				if (this.$route.name == 'CategoryCustom') {
					this.selected = this.$route.params.id
				}
				this.$api.setting.accountingCategory.list({account_sets_id: this.accountSetsId}).then(({data}) => {
					data.forEach(val => {
						this.tabDatas.push({
							title: val.name,
							key: val.id
						})
					});
				});
			},
			async reloadTabs() {
				await this.$api.setting.accountingCategory.list({account_sets_id: this.accountSetsId}).then(({data}) => {
					let tabDatas = [{
						title: '辅助核算类别',
						key: 'AccountingCategory'
					}];
					data.forEach(val => {
						tabDatas.push({
							title: val.name,
							key: val.id
						});
					});
					this.tabDatas = tabDatas;
				});
			},
      pushDatas(){
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountSetsId = this.currentAccountSets.id
        this.init();
      },
      accountsSetsChange(data) {
        //查询
        this.$store.dispatch('init', data.key).then(() => {
          this.doSearch();
        })
			  this.tabDatas=[{
          title: '辅助核算类别',
          key: 'AccountingCategory'
        }];
        this.init();
      },
      tabChange(val){
        if (val.key == 'AccountingCategory') {
          this.$router.push({name: "AccountingCategory", params: {accountSetsId: this.accountSetsId}})
        } else {
          this.$router.push({name: 'CategoryCustom', params: {id: val.key, accountSetsId: this.accountSetsId}})
        }
      }
		}
	};
</script>
<style lang='less'>
	div.h-panel-bar {
		border-top: 0px solid #eeeeee
	}
</style>


