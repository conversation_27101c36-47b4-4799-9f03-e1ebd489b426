<template>
	<app-content class="h-panel">
    <div style="height: 10px"></div>
<!--    <Form v-width="300" :labelWidth="80" >
      <FormItem label="账套:" prop="accountSetsId">
        <Select v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="accountsSetsChange"></Select>
      </FormItem>
    </Form>-->

		<div class="h-panel-bar"><span class="h-panel-title">凭证字</span></div>
		<div class="margin-top margin-left">
			<Button color="primary" @click="showForm=true">新增</Button>
		</div>
		<div class="h-panel-body">
			<Table :datas="datas" :border="true">
				<TableItem title="凭证字" prop="word"></TableItem>
				<TableItem title="打印标题" prop="printTitle" :width="120"></TableItem>
				<TableItem title="是否默认" :width="100">
					<template slot-scope="{data}">
						{{data.isDefault?'是':'否'}}
					</template>
				</TableItem>
				<TableItem title="操作" :width="100" align="center">
					<div class="actions" slot-scope="{data}">
						<span @click="edit(data)">编辑</span>
						<span @click="remove(data)">删除</span>
					</div>
				</TableItem>
			</Table>
		</div>
		<Modal v-model="showForm" hasCloseIcon type="drawer-right">
			<div slot="header">凭证字设置</div>
			<Form ref="form" v-width="400" :labelWidth="100" :model="form" :rules="validationRules">
				<FormItem label="凭证字" prop="word">
					<input type="text" v-model="form.word">
				</FormItem>
				<FormItem label="打印标题" prop="printTitle">
					<input type="text" v-model="form.printTitle">
				</FormItem>
				<FormItem label="是否默认" prop="isDefault">
					<Radio v-model="form.isDefault" dict="defaultRadios"></Radio>
				</FormItem>
			</Form>
			<div class="text-center">
				<Button color="green" @click="submit" :loading="loading">{{form.id?'更新':'保存'}}</Button>
				<Button @click="showForm=false">取消</Button>
			</div>
		</Modal>
	</app-content>
</template>

<script>
	const emptyForm = {
		"word": "",
		"printTitle": "",
		"isDefault": 0,
	};

	export default {
		name: 'VoucherWord',
		data() {
			return {
				datas: [],
				form: Object.assign({}, emptyForm),
				validationRules: {
					required: ["word", "printTitle", "isDefault"]
				},
				showForm: false,
				loading: false,
        //账套选中标识
        accountsSetsSelected: null,
        accountSetsList: []
			};
		},
    computed: {
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      },
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      }
    },
		watch: {
			showForm(val) {
				if (!val) {
					this.reset();
				}
			}
		},
		methods: {
			loadList() {
				this.$api.setting.voucherWord.list({account_sets_id: this.currentAccountSets.id}).then(({data}) => {
					this.datas = data || [];
				})
			},
			submit() {
				let validResult = this.$refs.form.valid();
				if (validResult.result) {
					this.loading = true;
          this.form.isDefault = this.form.isDefault == 0 ? false:true;
					this.form.accountSetsId = this.currentAccountSets.id;
					this.$api.setting.voucherWord[this.form.id ? 'update' : 'save'](this.form).then(() => {
						this.loadList();
						this.showForm = false;
						this.loading = false;
					}).catch(() => {
						this.loading = false;
					})
				}
			},
			reset() {
				this.form = Object.assign({}, emptyForm)
			},
			remove(data) {
				this.$Confirm("确认删除?").then(() => {
					this.$api.setting.voucherWord.delete(data.id).then(() => {
						this.loadList();
					})
				})
			},
			edit(data) {
				this.form = Object.assign({}, data)
				this.showForm = true;
			},
      pushDatas(){
        this.accountSetsList = [];
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountsSetsSelected = this.currentAccountSets.id;
      },
      accountsSetsChange(data) {
        //查询
        this.$store.dispatch('init', data.key).then(() => {
          this.loadList();
        })
      }
		},
		mounted() {
      this.pushDatas();
      this.loadList();
		}
	};
</script>
