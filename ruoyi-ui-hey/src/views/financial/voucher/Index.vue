<template>
	<app-content class="h-panel">
<!--		<div class="h-panel-bar"><span class="h-panel-title">凭证列表</span></div>-->
<!--    <div class="float-right h-input h-input-prefix-icon">
      <input type="text" placeholder="输入账套名称" v-model="accountSetsName" @keyup.enter="searchAccountSets"/>
      <i class="h-icon-search"></i>
    </div>
    <Tabs :datas="accountSetsList" v-model="accountsSetsSelected" @click="accountsSetsChange"></Tabs>-->

    <div style="height: 10px"></div>
<!--    <Form v-width="450" :labelWidth="60" >
      <FormItem label="账套:" prop="accountSetsId">
        <Row type="flex">
          <Cell width="20" style="margin-right: 5px">
            <Select v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="accountsSetsChange"></Select>
          </Cell>
        </Row>
      </FormItem>
    </Form>-->

		<div class="margin-right-left">
<!--			<account-date-choose v-model="accountDate"/>-->
      <Row>
        <Cell>
          <DateRangePicker v-model="value" :format="format" @confirm="doSearch" style="margin-right: 4px"></DateRangePicker>
          <template  v-if="currentAccountSets.voucherReviewed && User.role !== 'accounting'">
            <Button color="blue" :loading="loading" @click="batchAudit">审核</Button>
<!--            <Button :loading="loading" @click="cancelAudit">取消审核</Button>-->
          </template>
          <template>
            <Button color="blue" :loading="loading" @click="showReorder=true">凭证号排序</Button>
          </template>
<!--            				<DropdownMenu button @click="trigger" :datas="param">打印</DropdownMenu>-->
          <template v-if="User.role !=='onlyView' && User.role !== 'accounting'">
<!--                      <Button :loading="loading" @click="finishingOffNo">整理断号</Button>-->
            <Button color="red" :loading="loading" @click="batchDelete">批量删除</Button>
            <Button text-color="green" :loading="loading" @click="doUpload">
              <input ref="file" type="file" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" style="display: none" @change="fileChange($event)">
              导入
            </Button>
            <Button text-color="yellow" :loading="loading" @click="handleExport">
              <input ref="file" type="file" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" style="display: none" @change="fileChange($event)">
              导出
            </Button>
          </template>
        </Cell>

      </Row>
      <Tabs :datas="auditState" v-model="auditStateSelected" class="margin-top" @change="auditStateChange"></Tabs>
		</div>
		<div class="h-panel-body">
			<table class="header">
				<tr>
					<th style="width: 50px"><input :checked="checkAll" type="checkbox" @click="checkAll=!checkAll"></th>
					<td style="width: 215px">摘要</td>
					<td>科目</td>
					<td align="right" style="width: 130px">借方金额</td>
					<td align="right" style="width: 130px">贷方金额</td>
				</tr>
			</table>
			<table v-if="!datas.length">
				<tr>
					<td colspan="5" class="text-center padding">暂无数据</td>
				</tr>
			</table>
			<table class="details" v-for="data in datas" :key="data.id">
				<tr class="details-header">
					<th style="width: 50px"><input :class="{'display':data._checked}" v-model="data._checked" type="checkbox"></th>
          <td colspan="2">日期：{{data.voucherDate}}
            凭证字号：{{data.word}}-{{data.code}}
            状态：<span v-if="!data.valid" style="color: red">已作废</span><span v-if="data.valid">{{data.auditMemberId? '已审核':'待审核'}}</span>
            <Button v-if="currentAccountSets.voucherReviewed&&(data.auditMemberId==null||!data.auditMemberId) && data.valid" class="margin-left" color="blue" :loading="loading" @click="audit(data)">审核</Button>
            <Button style="margin-left:5px" color="blue" :loading="loading">
              <router-link tag="span" :to="{name:'VoucherForm',params:{voucherInfo:data, sourceType:'copy'}}">复制</router-link>
            </Button>
          </td>
					<td colspan="2" class="actions" align="right">
						<router-link tag="span" :to="{name:'VoucherForm',params:{voucherId:data.id,showDetail: true}}">查看</router-link>
            <!--						<router-link v-if="data.auditMemberId" tag="span" :to="{name:'VoucherForm',params:{voucherId:data.id}}">查看</router-link>-->
            <a :href="`${reqUrl}/pfd/voucher?id=${data.id}&accountSetsId=${accountsSetsSelected}&realName=${User.realName}`" target="_blank">打印</a>
            <router-link v-if="data.valid" tag="span" :to="{name:'VoucherForm',params:{voucherId:data.id}}">修改</router-link>
            <span v-if="data.valid" @click="remove(data)">删除</span>
            <span v-if="!data.auditMemberId" @click="valid(data)">{{data.valid ?'作废':'取消作废'}}</span>
            <i v-if="data.auditMemberId" class="h-icon-completed green-color" v-tooltip content="已审核"></i>
					</td>
				</tr>
				<tr v-for="d in data.details" :key="d.id" :class="{'un-valid': !data.valid}">
					<th></th>
					<td style="width: 215px">
						{{d.summary}}
						<template v-if="d.subject&&d.num&&d.price">
							(数量:{{d.num}}<span class="dark4-color">{{d.subject.unit}}</span>，单价:{{d.price}}<span class="dark4-color">元</span>)
						</template>
					</td>
					<td>{{d.subjectName}}</td>
					<td align="right" style="width: 130px">{{d.debitAmount|numFormat}}</td>
					<td align="right" style="width: 130px">{{d.creditAmount|numFormat}}</td>
				</tr>
				<tr class="font-bold" :class="{'un-valid': !data.valid}">
					<td></td>
					<td>合计</td>
					<td>{{data.debitAmount|dxMoney}}</td>
					<td align="right">{{data.debitAmount|numFormat}}</td>
					<td align="right">{{data.creditAmount|numFormat}}</td>
				</tr>
			</table>
			<Pagination v-model="pagination" @change="currentChange" layout="total,sizes,pager,jumper" align="center"/>
		</div>
    <Modal v-model="showReorder">
      <Form v-width="300" :labelWidth="100" >
        <FormItem label="年月:">
          <DatePicker v-model="reorderForm.voucherYearMonth" :type="'month'" placeholder="请选择年月"></DatePicker>
        </FormItem>
        <FormItem>
          <Button text-color="primary" @click="handleReorderCode">重新排序</Button>
        </FormItem>
      </Form>
    </Modal>

	</app-content>
</template>

<script>
	import Qs from "qs";
	import moment from 'moment'
	import {mapState} from 'vuex'
  import { download } from "@/utils/request";

	export default {
		name: "Voucher",
		data() {
			return {
			  reqUrl: process.env.VUE_APP_BASE_API,
				datas: [],
				accountDate: null,
				pagination: {
					page: 1,
          size: 10,
					total: 0
				},
				loading: false,
				checkAll: false,
				param: [{title: '打印全部', key: 'printAll'}, {title: '打印选中', key: 'printCheck'}],
        accountSetsList: [],
        auditState: [
          {title:'全部', key: '0'},
          {title:'待审核', key: '1'},
          {title:'已审核', key: '2'},
          {title:'已作废', key: '3'},
        ],
        //账套选中标识
        accountsSetsSelected: null,
        //审核状态选中标识
        auditStateSelected: '0',
        accountSetsName: "",
        format: 'YYYY-MM-DD',
        value:{ "start": moment(new Date()).startOf('month').format('YYYY-MM-DD'), "end": moment(new Date()).format('YYYY-MM-DD')},
        showReorder: false,
        reorderForm:{
          accountSetsId:0,
          voucherYearMonth: null,
        },
			};
		},
    created() {
		  this.$store.dispatch("init", this.currentAccountSets.id);
    },
    watch: {
			checkAll(nval) {
				let data = Array.from(this.datas);
				data.forEach(val => val._checked = nval);
				this.datas = data;
			},
			accountDate() {
				this.pagination.page = 1;
				this.loadList();
			}
		},
		computed: {
      User() {
        return this.$store.state.financial.User;
      },
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      },
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      },
			date() {
				return moment(this.accountDate);
			}
		},
    mounted() {
      //账套列表
      this.pushDatas()
      this.loadList()
    },
		methods: {
			currentChange() {
				this.loadList();
			},
			trigger(key) {
				this[key].call(this);
			},
			printAll() {
				let param = Qs.stringify({accountSetsId: accountsSetsSelected, startTime: this.value.start, endTime: this.value.end}, {indices: false});
				window.open(`${this.reqUrl}/pfd/voucher?${param}`);
			},
			printCheck() {
				let checked = this.datas.filter(value => value._checked);
				if (checked.length) {
					let param = Qs.stringify({id: checked.map(value => value.id), accountSetsId: accountsSetsSelected}, {indices: false});
					window.open(`${this.reqUrl}/pfd/voucher?${param}`);
				} else {
					this.$Message("未选择记录");
				}
			},
			loadList() {
			  let reqData = {account_sets_id: this.accountsSetsSelected,
          start_time: this.value.start,
          end_time: this.value.end,
          voucher_state: this.auditStateSelected,
          page: this.pagination.page,
          pageSize: this.pagination.size,
			  }

				this.$api.voucher.list(reqData).then(({data}) => {
					this.datas = data.records;
					this.pagination = {
						page: data.current,
						size: data.size,
						total: data.total
					}
				})
			},
			remove(data) {
				this.$Confirm("确认删除?").then(() => {
					this.$api.voucher.delete(data.id, {accountSetsId: data.accountSetsId}).then(() => {
						this.loadList();
					})
				});
			},
      valid(data) {
        this.$Confirm("确认作废?").then(() => {
          this.$api.voucher.valid(data.id, {accountSetsId: data.accountSetsId}).then(() => {
            this.loadList();
          })
        });
      },
			finishingOffNo() {
				this.loading = true;
				this.$api.voucher.finishingOffNo({startTime: this.value.start, endTime: this.value.end}).then(() => {
					this.loadList();
				}).finally(() => {
					this.loading = false
				});
			},
			batchDelete() {
				let checked = this.datas.filter(value => value._checked);
				if (checked.length) {
					this.$Confirm("确认删除?").then(() => {
						this.loading = true;
						this.$api.voucher.batchDelete({accountSetsId: this.accountsSetsSelected, checked: checked.map(value => value.id), startTime: this.value.start, endTime: this.value.end}).then(() => {
						  this.checkAll = false;
							this.loadList();
              this.$Message("删除成功！");
						}).finally(() => {
							this.loading = false
						});
					});
				}else{
          this.$Message("未选择记录");
        }
			},
			audit(data) {
        this.$Confirm("亲，确认要审核吗?").then(() => {
          this.loading = true;
          var voucherDate = new Date(data.voucherDate);
          this.$api.voucher.audit({accountSetsId: this.accountsSetsSelected, checked: [data.id], year: voucherDate.getFullYear(), month: voucherDate.getMonth()+1}).then(() => {
            this.loadList();
            this.$Message("审核成功！");
          }).catch(error => {
            this.$Message.error(error);
          }).finally(() => {
            this.loading = false;
          });
        });
			},
      batchAudit() {
        let checked = this.datas.filter(value => value._checked);
        if (checked.length) {
          this.$Confirm("亲，确认要审核吗?").then(() => {
            this.loading = true;
            this.$api.voucher.batchAudit({accountSetsId: this.accountsSetsSelected, checked: checked.map(value => value.id), startTime: this.value.start, endTime: this.value.end}).then(() => {
              this.loadList();
              this.$Message("审核成功！");
            }).finally(() => {
              this.loading = false;
            });
          });
        } else {
          this.$Message("未选择记录");
        }
      },
			cancelAudit() {
				let checked = this.datas.filter(value => value._checked);
				if (checked.length) {
					this.$Confirm("亲，确认要取消审核吗?").then(() => {
						this.loading = true;
						this.$api.voucher.cancelAudit({checked: checked.map(value => value.id), startTime: this.value.start, endTime: this.value.end}).then(() => {
							this.loadList();
							this.$Message("取消审核成功！");
						}).finally(() => {
							this.loading = false;
						});
					});
				} else {
					this.$Message("未选择记录");
				}
			},
			doUpload() {
				this.$refs.file.click();
			},
			fileChange(e) {
				if (this.$refs.file.files.length) {
					let formData = new FormData();
					formData.append('file', this.$refs.file.files[0]);
					formData.append('accountSetsId', this.currentAccountSets.id);
					this.loading = true;
					this.$api.voucher.import(formData).then(({data}) => {
						if (data) {
							this.accountDate = data;
						}
						this.$store.dispatch('init', this.currentAccountSets.id);
						this.$Message("亲,导入成功~");
					}).finally(() => {
						this.loading = false;
					});

					this.$refs.file.value = "";
				}
			},
      accountsSetsChange(data) {
        //查询
        this.$store.dispatch('init', data.key).then(() => {
          this.loadList();
        })
      },
      auditStateChange(){
			  this.loadList();
      },
      searchAccountSets(event){
        if(this.accountSetsName != ''){
          let accountSetsMap = {};
          this.myAccountSets.forEach(accountSets => {accountSetsMap[accountSets.companyName] =  accountSets.id})
          //查询
          this.$store.dispatch('init', accountSetsMap[this.accountSetsName]).then(() => {
            this.accountSetsList = [];
            let accountSet = {
              title: this.currentAccountSets.companyName,
              key: this.currentAccountSets.id
            }
            this.accountSetsList.push(accountSet)
          })

        }else{
          this.pushDatas()
        }
      },
      pushDatas(){
        this.accountSetsList = [];
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountsSetsSelected = this.currentAccountSets.id;
      },
      doSearch() {
        this.loadList();
      },
      //复制科目
      handleReorderCode(){
        if(this.reorderForm.voucherYearMonth == null){
          this.$Message("请选择年月");
          return;
        }
        this.reorderForm.accountSetsId = this.accountsSetsSelected;
        this.$api.voucher.reorderCode(this.reorderForm).then(({data}) => {
          this.loadList();
          this.$Message("排序成功！！！");
          this.showReorder = false
          this.reorderForm = {};
        }).catch(error => {
          this.$Message.error(error);
        });
      },
      /** 导出按钮操作 */
      async handleExport() {
        const queryParams = {
          accountSetsId: this.accountsSetsSelected,
          startTime: this.value.start,
          endTime: this.value.end,
          voucherState: this.auditStateSelected,
        };
        this.$Confirm('是否确认导出所选数据项?', '提⽰', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          download('voucher/exportVoucher', {
            ...queryParams
          }, `voucherDetail_${new Date().getTime()}.xlsx`)
        }).catch(() => {})
      },
		}
	};
</script>
<style lang='less' scoped>
  .un-valid{
    background-color: #9d9d9d;
  }
	.h-panel-body {
		table {
			width: 100%;
			border-collapse: collapse;

			td {
				padding: 7px;
			}

			&.header {
				background-color: @primary-color;
				color: white;
			}
		}

		.details {
			font-size: 12px;
			margin: 15px 0;
			border: 1px solid @gray2-color;

			.actions {
				text-align: right;
				padding-right: 20px;

				span, a {
					display: none;
				}
			}

			input {
				display: none;

				&.display {
					display: inline-block;
				}
			}

			&-header {
				background-color: @gray3-color;
				color: @dark3-color;
			}

			td, th {
				border-bottom: 1px solid @gray2-color;
			}

			tr:hover:not(.details-header) {
				background-color: #dff7df;
				cursor: pointer;
			}

			&:hover {
				box-shadow: 0 0 10px 0 #dadada;
				border-color: #dadada;

				.actions {
					span, a {
						display: inline-block;
					}
				}

				input {
					display: inline-block;
				}
			}
		}
	}
</style>
