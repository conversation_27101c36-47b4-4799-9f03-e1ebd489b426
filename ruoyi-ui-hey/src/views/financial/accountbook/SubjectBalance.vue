<template>
	<app-content class="h-panel">
<!--    <div class="h-input h-input-prefix-icon" style="float: right">
      <input type="text" placeholder="输入账套名称" v-model="accountSetsName" @keyup.enter="searchAccountSets"/>
      <i class="h-icon-search"></i>
    </div>
    <Tabs :datas="accountSetsList" v-model="accountsSetsSelected" @change="accountsSetsChange"></Tabs>-->
    <div style="height: 10px"></div>
<!--    <Form v-width="450" :labelWidth="60" >
      <FormItem label="账套:" prop="accountSetsId">
        <Row type="flex">
          <Cell width="20" style="margin-right: 5px">
            <Select v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="accountsSetsChange"></Select>
          </Cell>
        </Row>
      </FormItem>
    </Form>-->

		<div class="h-panel-bar">
			<span class="h-panel-title">科目余额表</span>
		</div>
		<div class="margin-right-left margin-top">
<!--			<account-date-choose v-model="accountDate"/>-->
      <Row>
        <Cell>
          科目名称：
          <Search @search="doSearch" v-model="value.subjectName" v-width="250" showSearchButton placeholder="模糊搜索"><i class="h-icon-search"></i></Search>
          日期：<DateRangePicker v-model="value" :format="format" @confirm="doSearch"></DateRangePicker>
          <Button text-color="yellow" style="margin-left: 5px" @click="handleExport">导出</Button>
        </Cell>
      </Row>
			<div class="float-right">
				<Checkbox v-model="showNumPrice">显示数量金额</Checkbox>
			</div>
		</div>
		<div class="h-panel-body">
			<table class="balance" v-if="!showNumPrice">
				<thead class="header">
				<tr>
					<td rowspan="2">科目编码</td>
					<td rowspan="2">科目名称</td>
					<td colspan="2">期初余额</td>
					<td colspan="2">本期发生额</td>
					<td colspan="2">期末余额</td>
				</tr>
				<tr>
					<td>借方</td>
					<td>贷方</td>
					<td>借方</td>
					<td>贷方</td>
					<td>借方</td>
					<td>贷方</td>
				</tr>
				</thead>
				<tbody>
				<tr v-for="item in dataList" :key="item.subjecId">
					<td :style="{'padding-left':(item.level)*10+'px'}">{{item.code}}</td>
					<td :style="{'padding-left':(item.level)*10+'px'}">
            <span class="text-hover" @click="openDetail(item)">{{item.name}}</span>
          </td>
					<td align="right">{{item.beginningDebitBalance|numFormat}}</td>
					<td align="right">{{item.beginningCreditBalance|numFormat}}</td>
					<td align="right">{{item.currentDebitAmount|numFormat}}</td>
					<td align="right">{{item.currentCreditAmount|numFormat}}</td>
					<td :class="{tip:item.endingDebitBalance<0}" align="right">{{item.endingDebitBalance|numFormat}}</td>
					<td :class="{tip:item.endingCreditBalance<0}" align="right">{{item.endingCreditBalance|numFormat}}</td>
				</tr>
				<tr v-if="!dataList.length">
					<td colspan="8" class="text-center padding">暂无数据</td>
				</tr>
				</tbody>
			</table>
			<table class="balance" v-else>
				<thead class="header">
				<tr>
					<td rowspan="2">科目编码</td>
					<td rowspan="2">科目名称</td>
					<td colspan="4">期初余额</td>
					<td colspan="4">本期发生额</td>
					<td colspan="4">期末余额</td>
				</tr>
				<tr>
					<td>借方数量</td>
					<td>借方金额</td>
					<td>贷方数量</td>
					<td>贷方金额</td>
					<td>借方数量</td>
					<td>借方金额</td>
					<td>贷方数量</td>
					<td>贷方金额</td>
					<td>借方数量</td>
					<td>借方金额</td>
					<td>贷方数量</td>
					<td>贷方金额</td>
				</tr>
				</thead>
				<tbody>
				<tr v-for="item in dataList" :key="item.subjecId">
					<td :style="{'padding-left':(item.level)*10+'px'}">{{item.code}}</td>
					<td :style="{'padding-left':(item.level)*10+'px'}"><span class="text-hover">{{item.name}}</span></td>
					<td class="text-right">&nbsp;</td>
					<td class="text-right" align="right">{{item.beginningDebitBalance|numFormat}}</td>
					<td class="text-right">&nbsp;</td>
					<td class="text-right" align="right">{{item.beginningCreditAmount|numFormat}}</td>

					<td class="text-right" align="right">{{item.currentDebitAmountNum|numFormat}}</td>
					<td class="text-right" align="right">{{item.currentDebitAmount|numFormat}}</td>
					<td class="text-right" align="right">{{item.currentCreditAmountNum|numFormat}}</td>
					<td class="text-right" align="right">{{item.currentCreditAmount|numFormat}}</td>

					<td class="text-right" align="right">{{item.endingDebitBalanceNum|numFormat}}</td>
					<td class="text-right" align="right">{{item.endingDebitBalance|numFormat}}</td>
					<td class="text-right" align="right">{{item.endingCreditBalanceNum|numFormat}}</td>
					<td class="text-right" align="right">{{item.endingCreditBalance|numFormat}}</td>
				</tr>
				<tr v-if="!dataList.length">
					<td colspan="14" class="text-center padding">暂无数据</td>
				</tr>
				</tbody>
			</table>
		</div>
	</app-content>
</template>

<script>
	import moment from "moment";
  import { download } from "@/utils/request";
  import SubjectBalanceDetail from "@/views/financial/accountbook/SubjectBalanceDetail";

  export default {
		name: "SubjectBalance",
		data() {
			return {
				dataList: [],
				accountDate: null,
				showNumPrice: false,
				loading: false,
        //账套选中标识
        accountsSetsSelected: null,
        accountSetsList: [],
        accountSetsName: "",
        format: 'YYYY-MM-DD',
        value:{ "start": '', "end": '', subjectName: ''},
        detailParams:{},
        openModal: false
			}
		},
		watch: {
			accountDate() {
				this.loadList();
			},
			showNumPrice() {
				this.loadList();
			}
		},
    computed: {
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      },
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      }
    },
    mounted() {
      //账套列表
      this.pushDatas()
      this.doSearch()
    },
		methods: {
			loadList() {
				this.loading = true;
        let reqData = {
          startTime: this.value.start,
          endTime: this.value.end,
          accountSetsId: this.currentAccountSets.id,
          showNumPrice: this.showNumPrice,
          subjectName: this.value.subjectName
        }
				this.$api.accountbook.loadSubjectBalance(reqData).then(({data}) => {
					this.dataList = data;
					this.loading = false;
				});
			},
      accountsSetsChange(data) {
        //查询
        this.$store.dispatch('init', data.key).then(() => {
          this.doSearch();
        })
      },
      pushDatas(){
        this.accountSetsList = [];
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountsSetsSelected = this.currentAccountSets.id;
      },
      searchAccountSets(event){
        if(this.accountSetsName != ''){
          let accountSetsMap = {};
          this.myAccountSets.forEach(accountSets => {accountSetsMap[accountSets.companyName] =  accountSets.id})
          //查询
          this.$store.dispatch('init', accountSetsMap[this.accountSetsName]).then(() => {
            this.accountSetsList = [];
            let accountSet = {
              title: this.currentAccountSets.companyName,
              key: this.currentAccountSets.id
            }
            this.accountSetsList.push(accountSet)
            this.doSearch();
          })

        }else{
          this.pushDatas()
        }
      },
      doSearch(){
        //this.accountDate = moment(this.currentAccountSets.currentAccountDate).format('YYYY-MM-DD');
        if(this.value.start == '' || this.value.start == undefined){
          this.value.start = moment(this.currentAccountSets.currentAccountDate).startOf('month').format('YYYY-MM-DD');
          this.value.end = moment(this.currentAccountSets.currentAccountDate).endOf('month').format('YYYY-MM-DD');
        }
        this.loadList();
      },
      /** 导出按钮操作 */
      async handleExport() {
        const queryParams = {
          startTime: this.value.start,
          endTime: this.value.end,
          accountSetsId: this.currentAccountSets.id,
          showNumPrice: this.showNumPrice
        };
        this.$Confirm('是否确认导出所选数据项?', '提⽰', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          download('accountBook/exportSubjectBalance', {
            ...queryParams
          }, `subjectBalance_${new Date().getTime()}.xlsx`)
        }).catch(() => {})
      },
      openDetail(item) {
        this.$Modal({
          component: {
            width: 1000,
            vue: SubjectBalanceDetail,
            datas: {subId: item.subjectId, subjectCode:item.code, startTime:this.value.start, endTime: this.value.end} // 子组件直接使用 props 即可使用，兼容性 1.15.0+
          },
          events: {
            close: (modal) => {
              modal.close();
            }
          }
        });
      }
		}
	}
</script>

<style scoped lang="less">
	.balance {
		width: 100%;
		border-collapse: collapse;

		td {
			padding: 0 8px;
			border: 1px solid #e2e2e2;
			font-size: 12px;
			height: 32px;

			&.tip {
				background-color: #FFEEEF;
			}
		}

		tbody tr:nth-child(even) {
			background-color: #f8fbf8;
		}

		tbody tr:hover {
			background-color: #F0F6FF;
		}

		.header {
			td {
				background-color: #F5F5F5;
				text-align: center;
				font-weight: bold;
				height: 35px;
			}
		}
	}
</style>
