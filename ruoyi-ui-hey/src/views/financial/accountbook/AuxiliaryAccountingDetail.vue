<template>
	<app-content class="h-panel">
<!--    <div class="h-input h-input-prefix-icon" style="float: right">
      <input type="text" placeholder="输入账套名称" v-model="accountSetsName" @keyup.enter="searchAccountSets"/>
      <i class="h-icon-search"></i>
    </div>
    <Tabs :datas="accountSetsList" v-model="accountsSetsSelected" @change="accountsSetsChange"></Tabs>-->
    <div style="height: 10px"></div>
<!--    <Form v-width="450" :labelWidth="60" >
      <FormItem label="账套:" prop="accountSetsId">
        <Row type="flex">
          <Cell width="20" style="margin-right: 5px">
            <Select v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="accountsSetsChange"></Select>
          </Cell>
        </Row>
      </FormItem>
    </Form>-->

		<div class="h-panel-bar">
			<span class="h-panel-title">
				<template v-if="auxiliary">{{auxiliary.name}}</template><template v-if="auxiliaryItem">:{{auxiliaryItem.name}}</template> 明细账</span>
		</div>
		<Row type="flex" :space-x="10" class="margin-right-left margin-top">
<!--			<Cell>
				<account-date-choose v-model="accountDate"/>
			</Cell>-->
      <Cell>
        <DateRangePicker v-model="value" :format="format"></DateRangePicker>
      </Cell>
			<Cell class="label">辅助类别：</Cell>
			<Cell>
				<Select type="object" v-model="auxiliary" :deletable="false" style="min-width: 100px" :datas="auxiliaryType" keyName="id" titleName="name"/>
			</Cell>
			<Cell>
				<Button :disabled="!auxiliary" :loading="loading" text-color="primary" @click="doSearch">查询</Button>
        <Button text-color="yellow" style="margin-left: 5px" @click="handleExport">导出</Button>
			</Cell>
			<Cell :flex="1">
				<!--<div class="float-right">
					<Checkbox v-model="showNumPrice">显示数量金额</Checkbox>
				</div>-->
			</Cell>
		</Row>
		<Row class="h-panel-body" type="flex" :space-x="10">
			<Cell :flex="1">
				<Table :datas="dataList" :border="true" :loading="loading">
					<TableItem title="日期" prop="voucherDate" :width="85"></TableItem>
					<TableItem title="凭证字号" :width="85">
						<template slot-scope="{data}" v-if="data.word">
							<router-link :to="{name:'VoucherForm',params:{voucherId:data.voucherId}}">{{data.word}}-{{data.code}}</router-link>
						</template>
					</TableItem>
					<TableItem title="摘要" prop="summary"></TableItem>
					<TableItem title="借方" align="right" :width="85">
						<template slot-scope="{data}">
							{{data.debitAmount|numFormat}}
						</template>
					</TableItem>
					<TableItem title="贷方" align="right" :width="85">
						<template slot-scope="{data}">
							{{data.creditAmount|numFormat}}
						</template>
					</TableItem>
					<TableItem title="方向" prop="balanceDirection" align="center" :width="50"></TableItem>
					<TableItem title="余额" align="right" :width="85">
						<template slot-scope="{data}">
							{{data.balance|numFormat}}
						</template>
					</TableItem>
				</Table>
			</Cell>
			<Cell v-width="200">
				<Tree @select="doSelect" :option="param" ref="auxiliary" filterable selectOnClick className="h-tree-theme-row-selected"></Tree>
			</Cell>
		</Row>
	</app-content>
</template>

<script>
	import moment from "moment";
  import { download } from "@/utils/request";

  export default {
		name: "AuxiliaryAccountingDetail",
		data() {
			return {
				dataList: [],
				loading: false,
				showNumPrice: false,
				param: {
					keyName: 'id',
					parentName: 'parentId',
					titleName: 'name',
					dataMode: 'list',
					datas: []
				},
				accountDate: null,
				auxiliary: null,
				auxiliaryItem: null,
				auxiliaryType: [],
        //账套选中标识
        accountsSetsSelected: null,
        accountSetsList: [],
        accountSetsName: "",
        format: 'YYYY-MM-DD',
        value:{ "start": '', "end": ''}
			}
		},
    computed: {
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      },
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      }
    },
		methods: {
			loadAuxiliaryType() {
				this.$api.setting.accountingCategory.list({account_sets_id: "2"}).then(({data}) => {
					this.auxiliaryType = data;
				});
			},
			loadList() {
				this.loading = true;
        let reqData = {
          startTime: this.value.start,
          endTime: this.value.end,
          accountSetsId: this.currentAccountSets.id,
          auxiliaryId: this.auxiliary.id,
          auxiliaryItemId: this.auxiliaryItem.id,
        }
				this.$api.accountbook.loadAuxiliaryDetails(reqData).then(({data}) => {
					this.dataList = data;
				}).finally(() => {
					this.loading = false;
				});
			},
			loadAuxiliaryList() {
				this.dataList = [];
        let reqData = {
          accountSetsId: this.currentAccountSets.id,
          auxiliaryId: this.auxiliary.id
        }
				this.$api.accountbook.loadAuxiliaryList(reqData).then(({data}) => {
					data.forEach(val => val.name = val.code + " " + val.name);
					data = data.sort((a, b) => a.code.localeCompare(b.code));
					this.$set(this.param, "datas", data);
					if (data.length) {
						this.auxiliaryItem = data[0];
						this.loadList();
						this.$nextTick(() => {
							this.$refs.auxiliary.updateSelect(this.auxiliaryItem.id);
						});
					}
				});
			},
			doSearch() {
        if(this.value.start == '' || this.value.start == undefined){
          this.value.start = moment(this.currentAccountSets.currentAccountDate).startOf('month').format('YYYY-MM-DD');
          this.value.end = moment(this.currentAccountSets.currentAccountDate).endOf('month').format('YYYY-MM-DD');
        }
				this.loadAuxiliaryList();
			},
			doSelect(node) {
				this.auxiliaryItem = node;
				this.loadList();
			},
      accountsSetsChange(data) {
        //查询
        this.$store.dispatch('init', data.key).then(() => {
          this.doSearch();
        })
      },
      pushDatas(){
        this.accountSetsList = [];
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountsSetsSelected = this.currentAccountSets.id;
      },
      searchAccountSets(event){
        if(this.accountSetsName != ''){
          let accountSetsMap = {};
          this.myAccountSets.forEach(accountSets => {accountSetsMap[accountSets.companyName] =  accountSets.id})
          //查询
          this.$store.dispatch('init', accountSetsMap[this.accountSetsName]).then(() => {
            this.accountSetsList = [];
            let accountSet = {
              title: this.currentAccountSets.companyName,
              key: this.currentAccountSets.id
            }
            this.accountSetsList.push(accountSet)
            this.doSearch();
          })

        }else{
          this.pushDatas()
        }
      },
      /** 导出按钮操作 */
      async handleExport() {
        const queryParams = {accountSetsId: this.currentAccountSets.id};
        this.$Confirm('是否确认导出所有数据项(包含历史数据)?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return this.$api.accountbook.exportDetail(queryParams);
        }).then(response => {
          download(response.msg);
        }).catch(function() {});
      }
		},
		mounted() {
			this.loadAuxiliaryType();
      //账套列表
      this.pushDatas()
      this.doSearch()
		}
	}
</script>

<style scoped>

</style>
