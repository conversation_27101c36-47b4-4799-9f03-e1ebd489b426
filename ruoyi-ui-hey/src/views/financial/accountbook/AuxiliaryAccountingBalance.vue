<template>
	<app-content class="h-panel">
<!--    <div class="h-input h-input-prefix-icon" style="float: right">
      <input type="text" placeholder="输入账套名称" v-model="accountSetsName" @keyup.enter="searchAccountSets"/>
      <i class="h-icon-search"></i>
    </div>
    <Tabs :datas="accountSetsList" v-model="accountsSetsSelected" @change="accountsSetsChange"></Tabs>-->
    <div style="height: 10px"></div>
<!--    <Form v-width="450" :labelWidth="60" >
      <FormItem label="账套:" prop="accountSetsId">
        <Row type="flex">
          <Cell width="20" style="margin-right: 5px">
            <Select v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="accountsSetsChange"></Select>
          </Cell>
        </Row>
      </FormItem>
    </Form>-->

		<div class="h-panel-bar">
			<span class="h-panel-title"><template v-if="auxiliary">{{auxiliary.name}} -</template> 核算项目余额</span>
		</div>
		<Row type="flex" :space-x="10" class="margin-right-left margin-top">
<!--			<Cell>
				<account-date-choose v-model="accountDate"/>
			</Cell>-->
      <Cell>
        <DateRangePicker v-model="value" :format="format"></DateRangePicker>
      </Cell>
			<Cell class="label">辅助类别：</Cell>
			<Cell>
				<Select type="object" v-model="auxiliary" :deletable="false" style="min-width: 100px" :datas="auxiliaryType" keyName="id" titleName="name"/>
			</Cell>
			<Cell>
        <Button :disabled="!auxiliary" :loading="loading" text-color="primary" @click="doSearch">查询</Button>
        <Button text-color="yellow" style="margin-left: 5px" @click="handleExport">导出</Button>
			</Cell>
			<Cell :flex="1">
				<!--<div class="float-right">
					<Checkbox v-model="showNumPrice">显示数量金额</Checkbox>
				</div>-->
			</Cell>
		</Row>
		<div class="h-panel-body">
			<table class="balance" v-if="auxiliary">
				<thead class="header">
				<tr>
					<td rowspan="2">{{auxiliary.name}}编码</td>
					<td rowspan="2">{{auxiliary.name}}名称</td>
					<td colspan="2">期初余额</td>
					<td colspan="2">本期发生额</td>
					<td colspan="2">期末余额</td>
				</tr>
				<tr>
					<td>借方</td>
					<td>贷方</td>
					<td>借方</td>
					<td>贷方</td>
					<td>借方</td>
					<td>贷方</td>
				</tr>
				</thead>
				<tbody>
				<tr v-for="item in dataList" :key="item.subjecId">
					<td :style="{'padding-left':(item.level)*10+'px'}">{{item.code}}</td>
					<td :style="{'padding-left':(item.level)*10+'px'}"><span class="text-hover">{{item.name}}</span></td>
					<td>{{item.beginningDebitBalance|numFormat}}</td>
					<td>{{item.beginningCreditBalance|numFormat}}</td>
					<td>{{item.currentDebitAmount|numFormat}}</td>
					<td>{{item.currentCreditAmount|numFormat}}</td>
					<td :class="{tip:item.endingDebitBalance<0}">{{item.endingDebitBalance|numFormat}}</td>
					<td :class="{tip:item.currentCreditAmount<0}">{{item.endingCreditBalance|numFormat}}</td>
				</tr>
				<tr v-if="!dataList.length">
					<td colspan="8" class="text-center padding">暂无数据</td>
				</tr>
				</tbody>
			</table>
		</div>
	</app-content>
</template>

<script>
	import moment from "moment";
  import { download } from "@/utils/request";

  export default {
		name: "AuxiliaryAccountingBalance",
		data() {
			return {
				dataList: [],
				showNumPrice: false,
				loading: false,
				accountDate: null,
				auxiliary: null,
				auxiliaryItem: {},
				auxiliaryType: [],
				auxiliaryList: [],
        //账套选中标识
        accountsSetsSelected: null,
        accountSetsList: [],
        accountSetsName: "",
        format: 'YYYY-MM-DD',
        value:{ "start": '', "end": ''}
			}
		},
    computed: {
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      },
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      }
    },
		methods: {
			loadAuxiliaryType() {
				this.$api.setting.accountingCategory.list({account_sets_id: "2"}).then(({data}) => {
					this.auxiliaryType = data;
					if (data.length) {
						this.auxiliary = data[0];
					}
				});
			},
			loadList() {
				if (this.auxiliary) {
					this.loading = true;
          let reqData = {
            startTime: this.value.start,
            endTime: this.value.end,
            accountSetsId: this.currentAccountSets.id,
            auxiliaryId: this.auxiliary.id,
          }
					this.$api.accountbook.loadAuxiliaryBalance(reqData).then(({data}) => {
						this.dataList = data;
					}).finally(() => {
						this.loading = false;
					});
				}

			},
			doSearch() {
        if(this.value.start == '' || this.value.start == undefined){
          this.value.start = moment(this.currentAccountSets.currentAccountDate).startOf('month').format('YYYY-MM-DD');
          this.value.end = moment(this.currentAccountSets.currentAccountDate).endOf('month').format('YYYY-MM-DD');
        }
				this.loadList();
			},
      accountsSetsChange(data) {
        //查询
        this.$store.dispatch('init', data.key).then(() => {
          this.doSearch();
        })
      },
      pushDatas(){
        this.accountSetsList = [];
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountsSetsSelected = this.currentAccountSets.id;
      },
      searchAccountSets(event){
        if(this.accountSetsName != ''){
          let accountSetsMap = {};
          this.myAccountSets.forEach(accountSets => {accountSetsMap[accountSets.companyName] =  accountSets.id})
          //查询
          this.$store.dispatch('init', accountSetsMap[this.accountSetsName]).then(() => {
            this.accountSetsList = [];
            let accountSet = {
              title: this.currentAccountSets.companyName,
              key: this.currentAccountSets.id
            }
            this.accountSetsList.push(accountSet)
            this.doSearch();
          })

        }else{
          this.pushDatas()
        }
      },
      /** 导出按钮操作 */
      async handleExport() {
        const queryParams = {accountSetsId: this.currentAccountSets.id};
        this.$Confirm('是否确认导出所有数据项(包含历史数据)?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return this.$api.accountbook.exportDetail(queryParams);
        }).then(response => {
          download(response.msg);
        }).catch(function() {});
      }
		},
		mounted() {
			this.loadAuxiliaryType();
      //账套列表
      this.pushDatas()
      this.doSearch()
		}
	}
</script>

<style scoped lang="less">
	.balance {
		width: 100%;
		border-collapse: collapse;

		td {
			padding: 0 8px;
			border: 1px solid #e2e2e2;
			font-size: 12px;
			height: 35px;

			&.tip {
				background-color: #FFEEEF;
			}
		}

		tbody tr:nth-child(even) {
			background-color: #f8fbf8;
		}

		tbody tr:hover {
			background-color: #F0F6FF;
		}

		.header {
			td {
				background-color: #F5F5F5;
				text-align: center;
				font-weight: bold;
			}
		}
	}
</style>
