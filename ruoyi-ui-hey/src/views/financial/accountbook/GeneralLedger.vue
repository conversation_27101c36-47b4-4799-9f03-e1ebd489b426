<template>
	<app-content class="h-panel">
<!--    <div class="h-input h-input-prefix-icon" style="float: right">
      <input type="text" placeholder="输入账套名称" v-model="accountSetsName" @keyup.enter="searchAccountSets"/>
      <i class="h-icon-search"></i>
    </div>
    <Tabs :datas="accountSetsList" v-model="accountsSetsSelected" @change="accountsSetsChange"></Tabs>-->
    <div style="height: 10px"></div>
<!--    <Form v-width="450" :labelWidth="60" >
      <FormItem label="账套:" prop="accountSetsId">
        <Row type="flex">
          <Cell width="20" style="margin-right: 5px">
            <Select v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="accountsSetsChange"></Select>
          </Cell>
        </Row>
      </FormItem>
    </Form>-->

		<div class="h-panel-bar">
			<span class="h-panel-title">总账</span>
		</div>
		<div class="margin-right-left margin-top">
<!--			<account-date-choose v-model="accountDate"/>-->
      <Row>
        <Cell>
          <DateRangePicker v-model="value" :format="format" @confirm="doSearch"></DateRangePicker>
          <Button text-color="yellow" style="margin-left: 5px" @click="handleExport">导出</Button>
        </Cell>
      </Row>
			<div class="float-right">
				<Checkbox v-model="showNumPrice">显示数量金额</Checkbox>
			</div>
		</div>
		<div class="h-panel-body margin" :space-x="10" style="overflow-x: auto;padding: 0;">
			<Loading text="总账数据加载中..." :loading="loading"></Loading>
			<table class="summary" v-if="!showNumPrice">
				<tr class="header bg-primary-color white-color">
					<td>科目编码</td>
					<td>科目名称</td>
					<td>期间</td>
					<td>摘要</td>
					<td>借方金额</td>
					<td>贷方金额</td>
					<td>方向</td>
					<td>余额</td>
				</tr>
				<template v-for="item in datalist">
					<tr class="bg-gray3-color">
						<td colspan="8">
							<span @click="toggleSummary(item)" class="text-hover">
								<i class="fa fa-caret-down" :class="{'fa-caret-right':item._expand}"></i>
								<span class="primary-color font-bold"> {{item.subject.name}}</span>
							</span>
						</td>
					</tr>
					<tr v-if="!item._expand" class="summary" v-for="s in item.summary">
						<td>{{item.subject.code}}</td>
						<td>{{item.subject.name}}</td>
						<td>{{s.voucherDate|fqFormat}}</td>
						<td>{{s.summary}}</td>
						<td align="right">{{s.debitAmount}}</td>
						<td align="right">{{s.creditAmount}}</td>
						<td align="center">{{s.balanceDirection}}</td>
						<td align="right">{{s.balance}}</td>
					</tr>
				</template>
				<tr v-if="!datalist.length">
					<td colspan="8" class="text-center padding">暂无数据</td>
				</tr>
			</table>
			<table v-else class="cus-table" style="width: 1780px;">
				<thead class="header">
				<tr>
					<td rowspan="2" width="100">科目编码</td>
					<td rowspan="2" width="100">科目名称</td>
					<td rowspan="2" width="80">单位</td>
					<td colspan="4">期初余额</td>
					<td colspan="2">本期借方</td>
					<td colspan="2">本期贷方</td>
					<td colspan="2">本年累计借方</td>
					<td colspan="2">本年累计贷方</td>
					<td colspan="4">期末余额</td>
				</tr>
				<tr>
					<td width="100">方向</td>
					<td width="100">数量</td>
					<td width="100">单价</td>
					<td width="100">金额</td>
					<td width="100">数量</td>
					<td width="100">金额</td>
					<td width="100">数量</td>
					<td width="100">金额</td>
					<td width="100">数量</td>
					<td width="100">金额</td>
					<td width="100">数量</td>
					<td width="100">金额</td>
					<td width="100">方向</td>
					<td width="100">数量</td>
					<td width="100">单价</td>
					<td width="100">金额</td>
				</tr>
				</thead>
				<tr v-for="{subject,summary} in datalist" :key="subject.id">
					<td>{{subject.code}}</td>
					<td>{{subject.name}}</td>
					<td class="text-center">{{subject.unit}}</td>
					<td class="text-center">{{summary[0].balanceDirection}}</td>
					<td class="text-right">{{summary[0].num}}</td>
					<td class="text-right">{{summary[0].price|numFormat}}</td>
					<td class="text-right">{{summary[0].balance|numFormat}}</td>
					<td class="text-right">{{summary[1].debitAmount ? summary[1].num:''}}</td>
					<td class="text-right">{{summary[1].debitAmount|numFormat}}</td>
					<td class="text-right">{{summary[1].creditAmount ? summary[1].num:''}}</td>
					<td class="text-right">{{summary[1].creditAmount|numFormat}}</td>
					<td class="text-right">{{summary[2].debitAmount ? summary[2].num:''}}</td>
					<td class="text-right">{{summary[2].debitAmount|numFormat}}</td>
					<td class="text-right">{{summary[2].creditAmount ? summary[2].num:''}}</td>
					<td class="text-right">{{summary[2].creditAmount|numFormat}}</td>
					<td class="text-center">{{summary[1].balanceDirection}}</td>
					<td class="text-right">{{summary[1].num}}</td>
					<td class="text-right">{{summary[1].price|numFormat}}</td>
					<td class="text-right">{{summary[1].balance|numFormat}}</td>
				</tr>
			</table>
		</div>
	</app-content>
</template>

<script>
	import moment from "moment";
  import { download } from "@/utils/request";

  export default {
		name: 'GeneralLedger',
		data() {
			return {
				loading: false,
				accountDate: null,
				showNumPrice: false,
				numPriceDisabled: true,
				datalist: [],
        //账套选中标识
        accountsSetsSelected: null,
        accountSetsList: [],
        accountSetsName: "",
        format: 'YYYY-MM-DD',
        value:{ "start": '', "end": ''}
			};
		},
		watch: {
			accountDate() {
				this.loadList();
			},
			showNumPrice() {
				this.loadList();
			}
		},
    computed: {
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      },
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      }
    },
    mounted() {
      //账套列表
      this.pushDatas()
      this.doSearch()
    },
		methods: {
			loadList() {
				this.loading = true;
        let reqData = {
          startTime: this.value.start,
          endTime: this.value.end,
          accountSetsId: this.currentAccountSets.id,
          showNumPrice: this.showNumPrice
        }
				this.$api.accountbook.loadGeneralLedger(reqData).then(({data}) => {
					this.datalist = data;
					this.loading = false;
				});
			},
			toggleSummary(item) {
				this.$set(item, '_expand', !item._expand)
			},
      accountsSetsChange(data) {
        //查询
        this.$store.dispatch('init', data.key).then(() => {
          this.doSearch();
        })
      },
      pushDatas(){
        this.accountSetsList = [];
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountsSetsSelected = this.currentAccountSets.id;
      },
      searchAccountSets(event){
        if(this.accountSetsName != ''){
          let accountSetsMap = {};
          this.myAccountSets.forEach(accountSets => {accountSetsMap[accountSets.companyName] =  accountSets.id})
          //查询
          this.$store.dispatch('init', accountSetsMap[this.accountSetsName]).then(() => {
            this.accountSetsList = [];
            let accountSet = {
              title: this.currentAccountSets.companyName,
              key: this.currentAccountSets.id
            }
            this.accountSetsList.push(accountSet)
            this.doSearch();
          })

        }else{
          this.pushDatas()
        }
      },
      doSearch(){
        //this.accountDate = moment(this.currentAccountSets.currentAccountDate).format('YYYY-MM-DD');
        if(this.value.start == '' || this.value.start == undefined){
          this.value.start = moment(this.currentAccountSets.currentAccountDate).startOf('month').format('YYYY-MM-DD');
          this.value.end = moment(this.currentAccountSets.currentAccountDate).endOf('month').format('YYYY-MM-DD');
        }
        this.loadList();
      },
      /** 导出按钮操作 */
      handleExport() {
        let queryParams = {
          startTime: this.value.start,
          endTime: this.value.end,
          accountSetsId: this.currentAccountSets.id,
          showNumPrice: this.showNumPrice
        };
        this.$Confirm('是否确认导出所选数据项?', '提⽰', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          download('accountBook/exportGeneralLedger', {
            ...queryParams
          }, `generalLedger_${new Date().getTime()}.xlsx`)
        }).catch(() => {})
      }
		}
	};
</script>

<style scoped lang="less">
	.summary {
		width: 100%;
		border-collapse: collapse;

		td {
			padding: 0 8px;
			border: 1px solid #eee;
			font-size: 12px;
			height: 32px;
		}

		.summary:nth-child(even) {
			background-color: #f8fbf8;
		}

		.summary:hover {
			background-color: #dff7df;
		}


		.header {
			td {
				text-align: center;
				font-weight: bold;
				height: 35px;
			}
		}
	}
</style>
