<template>
  <div>
    <el-dialog title="请选择凭证推送规则" :visible.sync="showDialog" width="1200px" append-to-body :before-close="handleCancel">
      <!-- <Dialog width="1200" :visible.sync="showDialog" title="请选择凭证推送规则"> -->
      <div class="search">
        <div class="item">
          <span>规则名称</span>
          <input style="width: 250px" v-model="params.ruleName" placeholder="请输入规则名称" type="text" />
        </div>
        <div class="item">
          <span>规则类型</span>
          <Select style="width: 250px" v-model="params.ruleType" placeholder="请选择规则类型" :datas="ruleTypes"
            keyName="dictValue" titleName="dictLabel" filterable />
        </div>
        <Button style="height: 32px; margin-right: 20px" @click="getList" color="blue" icon="h-icon-search">搜索</Button>
        <Button style="height: 32px" icon="h-icon-refresh" @click="reset">重置</Button>
        <Button @click="send" style="
            height: 40px;
            width: 150px;
            font-weight: bold;
            font-size: 16px;
            margin-left: 100px;
          " color="blue">推送凭证</Button>
      </div>
      <Table style="margin-top: 16px" :datas="tableData" ref="table" selectRow radio selectWhenClickTr
        @trclick="trclick">
        <TableItem title="规则名称" prop="ruleName"></TableItem>
        <TableItem title="所属账套" prop="" width="280">
          <template slot-scope="{ data }">
            <span class="tag" v-for="item in data.accounts" :key="item">
              {{ filterAcc(item).companyName }}
            </span>
          </template>
        </TableItem>
        <TableItem title="规则类型" prop="">
          <template slot-scope="{ data }">
            {{
              ruleTypes.filter((v) => data.ruleType == v.dictValue)[0].dictLabel
            }}</template>
        </TableItem>
        <TableItem title="创建时间" prop="createTime"></TableItem>
        <TableItem title="创建人" prop="createByName"></TableItem>
        <TableItem title="近期修改时间" prop="createTime"></TableItem>
        <TableItem title="近期修改人" prop="updateByName"></TableItem>
      </Table>
      <Pagination style="margin-top: 20px" v-model="pagination" @change="getList" layout="total,sizes,pager,jumper"
        align="center" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Dialog from "./Dialog.vue";
export default {
  components: {
    Dialog,
  },
  props: {
    accountId: Number,
  },
  data() {
    return {
      ruleTypes: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },

      params: {
        ruleName: "",
        ruleType: "",
        start: "",
        beginCreateTime: "",
        endCreateTime: "",
        accountId: "",
      },
      showDialog: true,
      tableData: [],
      selectData: null,
    };
  },
  computed: {
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    },
  },
  mounted() {
    this.getList();
    this.getDatas();
  },
  methods: {
    send() {
      if (!this.selectData) {
        this.$message.warning("请选择");
        return;
      }
      this.$emit("submit", this.selectData.id, this.params);
    },
    reset() {
      this.pagination = {
        page: 1,
        size: 10,
        total: 0,
      };

      this.params = {
        ruleName: "",
        ruleType: "",
      };
      this.getList();
    },
    filterAcc(e) {
      let data = this.myAccountSets.find((item) => {
        return item.id == e;
      });
      return data;
    },
    trclick(data) {
      console.log(data);
      this.selectData = data;
    },
    getList() {
      let data = {
        pageSize: this.pagination.size,
        pageNum: this.pagination.page,
        ...this.params,
        accountId: this.accountId,
      };
      this.$api.u8c.getChooseList(data).then((res) => {
        this.tableData = res.rows;

        this.pagination.total = res.total;
      });
    },
    handleCancel() {
      this.$emit("close");
    },
    handleOk() { },
    getDatas() {
      this.$api.u8c.getDicts("u8c_rule_type").then((res) => {
        this.ruleTypes = res.data;
      });
    },
  },
};
</script>

<style lang="less" scoped>
::deep .h-table td,
.h-table th {
  font-size: 15px !important;
}

.search {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .item {
    margin-right: 20px;

    display: flex;
    align-items: center;

    span {
      margin-right: 5px;
    }
  }
}

.tag {
  padding: 2px 6px;
  display: inline-block;
  border: 1px solid #d7d7d7;
  background: #e8f4ff;
  border-radius: 4px;
  margin-right: 10px;

  i {
    margin-left: 5px;
  }
}
</style>