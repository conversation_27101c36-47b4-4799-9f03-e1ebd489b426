<template>
  <transition name="fade">
    <div
      v-if="visible"
      class="custom-dialog-overlay"
      @click.self="handleOverlayClick"
    >
      <div class="custom-dialog" @click.stop :style="{ width: width + 'px' }">
        <div class="custom-dialog-header">
          <slot name="header">{{ title }}</slot>
          <button class="close-btn" @click="handleClose">×</button>
        </div>
        <div class="custom-dialog-body">
          <slot></slot>
        </div>
        <div class="custom-dialog-footer">
          <slot name="footer">
            <button @click="handleConfirm">确定</button>
          </slot>
        </div>
      </div>
    </div>
  </transition>
</template>
   
  <script>
export default {
  name: "CustomDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    width: {
      type: String,
    },
    title: {
      type: String,
      default: "提示",
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleConfirm() {
      this.$emit("confirm");
      this.handleClose(); // 通常确认后也会关闭弹窗，但可以根据需求调整
    },
    handleOverlayClick(event) {
      // 点击遮罩层时关闭弹窗，但通常不处理点击在弹窗内容上的事件
      return;
      if (event.target === event.currentTarget) {
        this.handleClose();
      }
    },
  },
};
</script>
   
  <style scoped>
.custom-dialog-overlay {
  position: fixed;
  top: -200px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* 确保弹窗在最上层 */
}

.custom-dialog {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  overflow: hidden;
  position: relative;
}

.custom-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
}

.custom-dialog-body {
  padding: 20px;
}

.custom-dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px;
  border-top: 1px solid #ebeef5;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>