<template>
  <div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="600px"
      append-to-body
      :before-close="handleClose"
    >
      <div>{{ str }}</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: true,
    };
  },
  props: {
    str: String,
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>