<template>
  <div>
    <Dialog
      width="600"
      :visible.sync="showDialog"
      @close="handleCancel"
      title="配置账套关联"
    >
      <div class="item">
        <span>智慧财务系统账套：</span>
        <input
          disabled
          type="text"
          style="width: 300px"
          v-model="params.accountName"
        />
      </div>

      <div class="item" style="align-items: start">
        <span><i>*</i>用友U8C系统账套：</span>
        <div>
          <Select
            style="width: 300px"
            v-model="params.pkGlorgbook"
            @change="changeMerchant"
            placeholder="请输入/选择账套"
            :datas="u8cList"
            keyName="pkGlorgbook"
            titleName="glorgbookName"
            filterable
          />
        </div>
      </div>
      <div class="item">
        <span>关联方式：</span>
        <Select
          style="width: 300px"
          v-model="params.relevanceType"
          placeholder="选择用友U8C客商后自动回显"
          disabled
          :datas="types.relevanceTypes"
          keyName="dictValue"
          titleName="dictLabel"
          filterable
        />
      </div>
      <div class="item" style="align-items: start">
        <span><i>*</i>修改原因：</span>
        <textarea
          style="width: 300px"
          v-model="params.updateReason"
          type="text"
        />
      </div>
      <template #footer>
        <Button style="margin-right: 16px" @click="handleCancel">取消</Button>
        <Button @click="handleOk" color="blue">确定</Button>
      </template>
    </Dialog>
  </div>
</template>
      
      <script>
import Dialog from "./Dialog.vue";
export default {
  props: {
    itemData: Object,
    u8cList: Array,
    types: Object,
  },
  components: {
    Dialog,
  },
  data() {
    return {
      merchantNames: [],
      params: {
        accountId: "",
        accountName: "",
        pkGlorgbook: "",
        glorgbookName: "",
        glorgbookCode: "",
        updateReason: "",
        pk_entityorg:"",
      },
      checkbox: false,
      showDialog: true,
    };
  },
  mounted() {
    this.params = Object.assign(this.params, this.itemData);

    if (this.params.relevanceType === undefined) {
      this.params.relevanceType = 1;
    } else {
      this.params.relevanceType = this.params.relevanceType * 1;
    }
    console.log(this.params);
  },

  methods: {
    changeMerchant(e) {
      console.log(e);
      this.params.glorgbookCode = e.glorgbookCode;
      this.params.glorgbookName = e.glorgbookName;
      this.params.pkGlorgbook = e.pkGlorgbook;
      this.params.pk_entityorg = e.pk_entityorg;
    },
    handleCancel() {
      this.$emit("close");
    },
    handleOk() {
      if (!this.params.pkGlorgbook) {
        this.$Message({ type: "warn", text: "请选择用友U8C系统账套" });
        return;
      }
      if (!this.params.updateReason) {
        this.$Message({ type: "warn", text: "请填写修改原因" });
        return;
      }

      this.params.isInterior = this.params.merchantType == 4 ? 0 : 1;
      this.$api.u8c.saveLedger({ ...this.params }).then((res) => {
        this.$Message({ type: "success", text: "修改成功" });
        this.$emit("close");
      });
      console.log(this.params);
    },
  },
};
</script>
      
      <style lang="less" scoped>
.item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  span {
    margin-right: 10px;
    display: inline-block;
    width: 140px;
    text-align: right;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
</style>