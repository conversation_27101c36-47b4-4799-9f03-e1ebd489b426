<template>
  <div>
    <Dialog
      width="1200"
      :visible.sync="showDialog"
      @close="handleCancel"
      title="智慧财务系统与用友U8C银行账户关联关系修改记录"
    >
      <el-table :data="datas" style="width: 100%; margin-top: 12px" border="">
        <el-table-column
          prop="u8cMerchantName"
          align="left"
          label="智慧财务系统银行账户/账号"
        >
          <template slot-scope="scope">
            {{ scope.row.bankOfDeposit }}{{ scope.row.accountNumber }}
          </template>
        </el-table-column>
        <el-table-column
          prop="u8cMerchantName"
          align="left"
          label="修改前用友U8C系统银行账户/账号"
        >
          <template slot-scope="scope">
            {{ scope.row.u8cAccountNameBefor }}{{ scope.row.u8cAccountBefor }}
          </template>
        </el-table-column>
        <el-table-column
          prop="u8cMerchantName"
          align="left"
          label="修改后用友U8C系统银行账户/账号"
        >
          <template slot-scope="scope">
            {{ scope.row.u8cAccountName }}{{ scope.row.u8cAccount }}
          </template>
        </el-table-column>
        <el-table-column prop="updateReason" align="left" label="修改原因">
        </el-table-column>
        <el-table-column prop="updateBy" align="left" label="修改人" width="120">
        </el-table-column>
        <el-table-column prop="updateTime" align="left" label="修改时间" width="200">
        </el-table-column>
      </el-table>
      <Pagination
        style="margin-top: 20px"
        v-model="pagination"
        @change="getList"
        layout="total,sizes,pager,jumper"
        align="center"
      />
      <template #footer>
        <Button style="margin-right: 16px" @click="handleCancel">取消</Button>
      </template>
    </Dialog>
  </div>
</template>
  
  <script>
import Dialog from "./Dialog.vue";
export default {
  components: {
    Dialog,
  },
  props: {
    id: String,
    accountId: String,
  },
  data() {
    return {
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
      showDialog: true,
      datas: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    handleCancel() {
      this.$emit("close");
    },
    getList() {
      let data = {
        pageNum: this.pagination.page,
        pageSize: this.pagination.size,
      };
      this.$api.u8c
        .selectUpdateBank({
          accountId: this.accountId,
          traderId: this.id,
          ...data,
        })
        .then((res) => {
          this.datas = res.rows;
          this.pagination.total = res.total;
        });
    },
  },
};
</script>
  
  <style>
</style>