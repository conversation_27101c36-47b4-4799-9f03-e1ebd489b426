<template>
  <div>
    <el-dialog
      title="查看推送记录"
      :visible.sync="dialogVisible"
      append-to-body
      width="1000px"
      :before-close="handleClose"
    >
      <el-table :data="tableData" border="" style="width: 100%">
        <el-table-column prop="pushType" label="凭证推送类型" />
        <el-table-column prop="ruleName" label="凭证推送时所选规则">
          <template slot-scope="scope">
            <el-button type="text" @click="detail(scope.row)">{{
              scope.row.rule ? scope.row.rule.ruleName : ""
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="pushStatus" label="推送状态" />
        <el-table-column prop="failureReason" label="推送失败原因" />
        <el-table-column prop="u8cNo" label="生成用友u8c凭证号" />
        <el-table-column prop="pushBy" label="推送操作人" />
        <el-table-column prop="pushTime" label="推送时间" />
      </el-table>
      <Pagination
        style="margin-top: 20px"
        v-model="pagination"
        @change="getList"
        layout="total,sizes,pager,jumper"
        align="center"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
    <AddEditConfig
      :detailType="true"
      v-if="AddEditConfigType"
      @close="AddEditConfigType = false"
      :itemData="itemData"
    />
  </div>
</template>

<script>
import AddEditConfig from "./AddEditConfig.vue";

export default {
  components: {
    AddEditConfig,
  },
  props: {
    id: Number,
  },
  data() {
    return {
      AddEditConfigType: false,
      dialogVisible: true,
      tableData: [],
      itemData: null,
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.$api.u8c
        .setVoucherDetail({ voucherId: this.id, ...this.pagination })
        .then((res) => {
          this.tableData = res.rows;
          this.pagination.total = res.total;
        });
    },
    detail(v) {
      this.itemData = v.rule;
      this.AddEditConfigType = true;
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>