<template>
  <div>
    <Dialog
      width="1200"
      :visible.sync="showDialog"
      @close="handleCancel"
      title="配置客商档案关联"
    >
      <Table :datas="datas" border>
        <TableItem
          title="用友U8C系统客商档案"
          prop="u8cMerchantName"
        ></TableItem>
        <TableItem
          title="修改前智慧财务系统客商"
          prop="merchantNameBefor"
        ></TableItem>
        <TableItem
          title="修改后智慧财务系统客商"
          prop="merchantName"
        ></TableItem>
        <TableItem title="修改原因" prop="updateReason"></TableItem>
        <TableItem title="修改人" prop="createByName"></TableItem>
        <TableItem title="修改时间" prop="createTime"></TableItem>

        <div slot="empty">暂无数据</div>
      </Table>
      <Pagination
        style="margin-top: 20px"
        v-model="pagination"
        @change="getList"
        layout="total,sizes,pager,jumper"
        align="center"
      />
      <template #footer>
        <Button style="margin-right: 16px" @click="handleCancel">取消</Button>
      </template>
    </Dialog>
  </div>
</template>

<script>
import Dialog from "./Dialog.vue";
export default {
  components: {
    Dialog,
  },
  props: {
    id: String,
  },
  data() {
    return {
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
      showDialog: true,
      datas: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    handleCancel() {
      this.$emit("close");
    },
    getList() {
      let data = {
        pageNum: this.pagination.page,
        pageSize: this.pagination.size,
      };
      this.$api.u8c
        .selectUpdate({ u8cMerchantId: this.id, ...data })
        .then((res) => {
          this.datas = res.rows;
          this.pagination.total = res.total;
        });
    },
  },
};
</script>

<style>
</style>