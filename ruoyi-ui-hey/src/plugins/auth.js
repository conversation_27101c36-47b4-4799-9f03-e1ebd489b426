import store from '@/store'

function authPermission(permission) {
    const all_permission = "*:*:*";
    const permissions = store.getters && store.getters.permissions
    console.log(permissions);
    if (permission && permission.length > 0) {
        return permissions.some(v => {
            return all_permission === v || v === permission
        })
    } else {
        return false
    }
}



export default {
    // 验证用户是否具备某权限
    hasPermi(permission) {
        return authPermission(permission);
    },

}
