import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

export default new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: [
    {
      path: '/voucher',
      name: 'Voucher',
      component: () => import('@/views/financial/voucher/Index'),
      meta: {
        title: '凭证列表',
        cache: true
      }
    },
    {
      path: '/u8cPush',
      name: 'U8cPush',
      component: () => import('@/views/financial/voucherPush/u8cPush'),
      meta: {
        title: '凭证推送至用友U8C',
        cache: true
      }
    },
    {
      path: '/configVoucher',
      name: 'configVoucher',
      component: () => import('@/views/financial/voucherPush/configVoucher'),
      meta: {
        title: '配置凭证推送规则',
        cache: true
      }
    },
    {
      path: '/withAccount',
      name: 'withAccount',
      component: () => import('@/views/financial/voucherPush/withAccount'),
      meta: {
        title: '关联用友U8C账套',
        cache: true
      }
    },
    {
      path: '/withCustomer',
      name: 'withCustomer',
      component: () => import('@/views/financial/voucherPush/withCustomer'),
      meta: {
        title: '关联用友U8C客商档案',
        cache: true
      }
    },
    {
      path: '/withBank',
      name: 'withBank',
      component: () => import('@/views/financial/voucherPush/withBank'),
      meta: {
        title: '关联用友U8C银行账户',
        cache: true
      }
    },
    {
      path: '/voucher/redirect/:accountSetsId(\\d+)/:voucherId(\\d+)',
      name: 'RedirectVoucherForm',
      component: () => import('@/views/financial/voucher/RedirectVoucherForm'),
      props: true,
      meta: {
        title: '重定向凭证信息'
      }
    },
    {
      path: '/voucher/form/:voucherId(\\d+)?',
      name: 'VoucherForm',
      component: () => import('@/views/financial/voucher/VoucherForm'),
      props: true,
      meta: {
        title: '凭证信息',
      }
    },
    {
      path: '/check-out',
      name: 'CheckOut',
      component: () => import('@/views/financial/checkout/Index'),
      children: [
        {
          path: 'list',
          name: 'CheckList',
          component: () => import('@/views/financial/checkout/CheckList'),
          meta: {
            title: '结账'
          }
        },
        {
          path: 'un-check',
          name: 'UnCheckOut',
          component: () => import('@/views/financial/checkout/UnCheckOut'),
          meta: {
            title: '反结账'
          }
        }, {
          path: 'carry-forward/:checkYear(\\d+)/:checkMonth(\\d+)',
          name: 'CarryForward',
          props: true,
          component: () => import('@/views/financial/checkout/CarryForward'),
          meta: {
            title: '月末结转'
          }
        }, {
          path: 'check/:checkYear(\\d+)/:checkMonth(\\d+)',
          name: 'Check',
          props: true,
          component: () => import('@/views/financial/checkout/Check'),
          meta: {
            title: '检查'
          }
        }
      ]
    },
    {
      path: '/account',
      name: 'Account',
      component: () => import('@/views/financial/setting/Account'),
      meta: {
        title: '账套'
      }
    },
    {
      path: '/subject',
      name: 'Subject',
      component: () => import('@/views/financial/setting/Subject'),
      meta: {
        title: '科目'
      }
    },
    {
      path: '/initial',
      name: 'Initial',
      component: () => import('@/views/financial/setting/Initial'),
      meta: {
        title: '期初'
      }
    },
    {
      path: '/voucher-word',
      name: 'VoucherWord',
      component: () => import('@/views/financial/setting/VoucherWord'),
      meta: {
        title: '凭证字'
      }
    },
    {
      path: '/currency',
      name: 'Currency',
      component: () => import('@/views/financial/setting/Currency'),
      meta: {
        title: '币别'
      }
    },
    {
      path: '/assisting-accounting',
      name: 'AssistingAccounting',
      component: () => import('@/views/financial/setting/AssistingAccounting'),
      meta: {
        title: '辅助核算'
      },
      children: [
        {
          path: 'accounting-category',
          name: 'AccountingCategory',
          component: () => import('@/views/financial/setting/AssistingAccounting/AccountingCategory'),
          props: true,
          meta: {
            title: '辅助核算类别'
          }
        },
        {
          path: 'custom/:id(\\d+)',
          name: 'CategoryCustom',
          component: () => import('@/views/financial/setting/AssistingAccounting/CategoryCustom'),
          props: true,
          meta: {
            title: '自定类别'
          }
        }
      ]
    },
    {
      path: '/template',
      component: () => import('@/views/financial/setting/template/Index'),
      meta: {
        title: '模板管理'
      },
      children: [{
        path: 'manager',
        name: 'TemplateManager',
        component: () => import('@/views/financial/setting/template/TemplateManager'),
        meta: {
          title: '模板管理'
        }
      }, {
        path: 'design/:templateId(\\d+)?',
        name: 'TemplateDesign',
        component: () => import('@/views/financial/setting/template/TemplateDesign'),
        props: true,
        meta: {
          title: '模板设计'
        }
      }]
    },
    {
      path: '/permission-setting',
      name: 'PermissionSetting',
      component: () => import('@/views/financial/setting/PermissionSetting'),
      meta: {
        title: '权限设置'
      }
    },
    {
      path: '/personal',
      name: 'Personal',
      component: () => import('@/views/financial/personal/Index'),
      meta: {
        title: '个人设置'
      },
      children: [
        {
          path: 'personal-setting',
          name: 'PersonalSetting',
          component: () => import('@/views/financial/personal/PersonalSetting'),
          meta: {
            title: '个人设置'
          }
        },
        {
          path: 'change-password',
          name: 'ChangePassword',
          component: () => import('@/views/financial/personal/ChangePassword'),
          meta: {
            title: '修改密码'
          }
        },
        {
          path: 'change-phone-number',
          name: 'ChangePhoneNumber',
          component: () => import('@/views/financial/personal/ChangePhoneNumber'),
          meta: {
            title: '修改手机'
          }
        },
        {
          path: 'binding-webchat',
          name: 'BindingWebchat',
          component: () => import('@/views/financial/personal/BindingWebchat'),
          meta: {
            title: '绑定微信'
          }
        }
      ]
    },
    {
      path: '/account-book/detailed',
      name: 'DetailedAccounts',
      component: () => import('@/views/financial/accountbook/DetailedAccounts'),
      meta: {
        title: '明细账'
      }
    },
    {
      path: '/account-book/journal',
      name: 'JournalAccounts',
      component: () => import('@/views/financial/accountbook/JournalAccounts'),
      meta: {
        title: '序时账'
      }
    },
    {
      path: '/account-book/general-ledger',
      name: 'GeneralLedger',
      component: () => import('@/views/financial/accountbook/GeneralLedger'),
      meta: {
        title: '总账'
      }
    },
    {
      path: '/account-book/subject-balance',
      name: 'SubjectBalance',
      component: () => import('@/views/financial/accountbook/SubjectBalance'),
      meta: {
        title: '科目余额',
        cache:true,
      }
    },
    {
      path: '/account-book/subject-balance-detail',
      name: 'SubjectBalanceDetail',
      props: true,
      component: () => import('@/views/financial/accountbook/SubjectBalanceDetail'),
      meta: {
        title: '科目余额明细'
      }
    },
    {
      path: '/account-book/subject-balance-voucher',
      name: 'SubjectBalanceVoucherForm',
      props: true,
      component: () => import('@/views/financial/accountbook/SubjectBalanceVoucherForm'),
      meta: {
        title: '科目余额明细'
      }
    },
    {
      path: '/account-book/subject-summary',
      name: 'SubjectSummary',
      component: () => import('@/views/financial/accountbook/SubjectSummary'),
      meta: {
        title: '科目汇总'
      }
    },
    {
      path: '/account-book/auxiliary-accounting-balance',
      name: 'AuxiliaryAccountingBalance',
      component: () => import('@/views/financial/accountbook/AuxiliaryAccountingBalance'),
      meta: {
        title: '辅助核算余额'
      }
    },
    {
      path: '/account-book/auxiliary-accounting-detail',
      name: 'AuxiliaryAccountingDetail',
      component: () => import('@/views/financial/accountbook/AuxiliaryAccountingDetail'),
      meta: {
        title: '辅助核算明细账'
      }
    },
    {
      path: '/report',
      name: 'ReportList',
      component: () => import('@/views/financial/report/ReportList'),
      meta: {
        title: '报表'
      }
    },
    {
      path: '/report/view/:reportId(\\d+)',
      name: 'ReportView',
      props: true,
      component: () => import('@/views/financial/report/ReportView'),
      meta: {
        title: '报表数据'
      }
    },
    {
      path: '/report/template',
      name: 'ReportTemplate',
      component: () => import('@/views/financial/report/template/TemplateList'),
      meta: {
        title: '报表模板'
      }
    },
    {
      path: '/report/template/form/:templateId(\\d+)',
      name: 'TemplateForm',
      props: true,
      component: () => import('@/views/financial/report/template/TemplateForm'),
      meta: {
        title: '模板编辑'
      }
    },
     {
      path: '/redirect/:path(.*)',
      name: 'Redirect',
      component: () => import('@/views/redirect'),}

  ]
});
