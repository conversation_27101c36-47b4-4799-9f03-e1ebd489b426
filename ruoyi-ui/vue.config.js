'use strict'
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}
const TerserPlugin = require('terser-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin')

var webpack = require('webpack') // 设置富文本编辑框文件拉伸大小

const name = process.env.VUE_APP_TITLE || '若依管理系统' // 网页标题

const port = process.env.port || process.env.npm_config_port || process.env.PROT // 端口 测试环境为21008 生产环境为11008

const timeStamp = new Date().getTime()
// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: "/",//process.env.NODE_ENV === "development" ? "/" : "/",
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: 'dist',
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: 'static',
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: false,//process.env.NODE_ENV === 'development',
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。

  productionSourceMap: false,
  // webpack-dev-server 相关配置
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        target: `http://localhost:`+8080,//测试环境为21007 生产环境为11007
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    },
    disableHostCheck: true
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: "expanded" }
      },
      //k-form-design主题配置
      less: {
        modifyVars: {
          "primary-color": "#2c87d0",
          "layout-color": "#9867f7",
          "left-right-width": "320px"
        },
        javascriptEnabled: true
      }
    },
    extract: { // 打包后css文件名称添加时间戳
      filename: `static/css/[name].${timeStamp}.css`,
      chunkFilename: `static/css/chunk.[id].${timeStamp}.css`,
    }
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    plugins: [
      // http://doc.ruoyi.vip/ruoyi-vue/other/faq.html#使用gzip解压缩静态文件
      new CompressionPlugin({
        test: /\.(js|css|html)?$/i,     // 压缩文件格式
        filename: '[path].gz[query]',   // 压缩后的文件名
        algorithm: 'gzip',              // 使用gzip压缩
        minRatio: 0.8                   // 压缩率小于1才会压缩
      }),
      // 设置富文本编辑框文件拉伸大小
      new webpack.ProvidePlugin({
        'window.Quill': 'quill/dist/quill.js',
        'Quill': 'quill/dist/quill.js'
      })
    ],
    output: {
      filename: `static/js/[name].${timeStamp}.js`,
      chunkFilename: `static/js/[name].${timeStamp}.js`
    },
  },
  chainWebpack(config) {
    config.plugins.delete('preload') //  need test
    config.plugins.delete('prefetch') //  need test
    config.optimization.minimizer('terser').use(TerserPlugin, [{
      terserOptions: {
        compress: {
          warnings: false,
          drop_console: true, // 去除console
          drop_debugger: true, // 去除debugger
        },
      },
      extractComments: false, // 是否将注释提取到单独的文件中
    }]);

    // 修改打包后media文件名
    config.module
      .rule("media")
      .use("url-loader")
      .tap(options => {
        options.name = `static/media/[name].${timeStamp}.[ext]`;
        options.fallback = {
          loader: "file-loader",
          options: {
            name: `static/media/[name].${timeStamp}.[ext]`
          }
        };
        return options;
      });
    // 修改打包后fonts文件名
    config.module
      .rule("fonts")
      .use("url-loader")
      .tap(options => {
        options.name = `static/fonts/[name].${timeStamp}.[ext]`;
        options.fallback = {
          loader: "file-loader",
          options: {
            name: `static/fonts/[name].${timeStamp}.[ext]`
          }
        };
        return options;
      });
    // 修改打包后img文件名
    config.module
      .rule("images")
      .use("url-loader")
      .tap(options => {
        options.name = `static/img/[name].${timeStamp}.[ext]`;
        options.fallback = {
          loader: "file-loader",
          options: {
            name: `static/img/[name].${timeStamp}.[ext]`
          }
        };
        return options;
      });

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()


    config
      //.when(process.env.NODE_ENV !== 'development',
      .when(true,
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
            chunks: 'all',
            cacheGroups: {
              libs: {
                name: 'chunk-libs',
                test: /[\\/]node_modules[\\/]/,
                priority: 10,
                chunks: 'initial' // only package third parties that are initially dependent
              },
              elementUI: {
                name: 'chunk-elementUI', // split elementUI into a single package
                priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
              },
              commons: {
                name: 'chunk-commons',
                test: resolve('src/components'), // can customize your rules
                minChunks: 3, //  minimum common number
                priority: 5,
                reuseExistingChunk: true
              }
            }
          })
          config.optimization.runtimeChunk('single'),
            {
              from: path.resolve(__dirname, './public/robots.txt'), //防爬虫文件
              to: './' //到根目录下
            }
        }
      )
  }
}
