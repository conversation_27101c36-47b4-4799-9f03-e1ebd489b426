<template>
  <div>
    <el-dialog
      title="选择项目"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <p v-if="type == 1">
        选择在 [新项目设置] 中已创建的新项目名称。此项目装入数据后将在
        [展示系统] 中展示
      </p>
      <p v-else>选择智慧平台中的项目。新项目将从此项目装入借据数据</p>
      <div>
        <span style="margin-right: 9px">担保公司</span>
        <el-select
          v-if="type == 1"
          v-model="params.guaranteeCompanyId"
          placeholder="请选择担保公司"
          clearable
          filterable=""
          size="small"
        >
          <el-option
            v-for="item in companyList"
            :key="item.companyId"
            :value="item.companyId"
            :label="item.companyName"
          />
        </el-select>
        <el-select
          @change="changeCompany"
          v-else
          v-model="params.companyId"
          placeholder="请选择担保公司"
          clearable
          filterable=""
          size="small"
        >
          <el-option
            v-for="item in companyList"
            :key="item.companyId"
            :value="item.companyId"
            :label="item.companyName"
          />
        </el-select>
        <span style="margin: 0 9px 0 16px">项目名称</span>
        <el-select
          v-model="params.projectId"
          placeholder="请选择新项目名称"
          clearable
          filterable=""
          size="small"
        >
          <el-option
            v-for="item in projectList"
            :key="item.projectId"
            :value="item.projectId"
            :label="item.projectName"
          />
        </el-select>
        <el-button
          style="margin-left: 20px"
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="search"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="reset"
          >重置</el-button
        >
      </div>
      <el-table :data="tableData" style="width: 100%; margin-top: 16px">
        <el-table-column label="选择" width="50" align="center">
          <template slot-scope="scope">
            <el-radio
              v-model="currentFactor"
              :label="scope.row"
              @input="clickChange"
              >{{ "" }}</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="productName" label="产品名称"  v-if="type == 2"/>
        <el-table-column
          prop="guaranteeCompanyShortName"
          label="担保公司"
          v-if="type == 1"
        />

        <el-table-column prop="date" label="已添加管控规则" v-if="type == 1">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.controlRulesType == 1">是</el-tag>
            <el-tag v-else type="danger">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="是否管控中" v-if="type == 1">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isControlFlag == 1">是</el-tag>
            <el-tag v-else type="danger">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="companyShortName"
          label="担保公司"
          v-if="type == 2"
        />
        <el-table-column
          prop="loanTotalAmount"
          label="累计放款金额"
          v-if="type == 2"
        >
         <template slot-scope="scope">
          {{ $formaterMoney(scope.row.loanTotalAmount) }}
         </template>
        </el-table-column>
        <el-table-column
          prop="loanTotalPlanBalanceAmt"
          label="当前在贷余额"
          v-if="type == 2"
        >
        <template slot-scope="scope">
          {{ $formaterMoney(scope.row.loanTotalPlanBalanceAmt) }}
         </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="params.pageNum"
        :limit.sync="params.pageSize"
        @pagination="changePagin"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";

import {
  queryCompanyInfo,
  queryNewProject,
  queryProjectDetails,
  getOldProjectInfo,
  getOldProjectLoanInfo,
} from "@/api/businessSystem/financialServices";
export default {
  props: {
    type: Number,
  },
  data() {
    return {
      currentFactor: "",
      dialogVisible: true,
      tableData: [],
      params: {},

      total: 0,
      projectList: [],
      companyList: [],
      selectData: null,
    };
  },
  mounted() {
    this.getData();
    this.queryNewProject();
    this.queryCompanyInfo();
  },
  methods: {
    changePagin() {
      if (this.type == 1) {
        this.getList();
      } else {
        this.getList2();
      }
    },

    getData() {
      console.log(this.type);
      if (this.type == 1) {
        this.params = {
          controlType: "1",
          pageNum: 1,
          pageSize: 10,
          projectId: "",
          guaranteeCompanyId: "",
        };
        this.getList();
      } else {
        this.params = {
          pageNum: 1,
          pageSize: 10,
          controlType: "1",
          projectId: "",
          companyId: "",
        };
        this.getList2();
      }
    },
    changeCompany(v) {
      getOldProjectInfo({ companyId: v }).then((res) => {
        this.projectList = res.data;
      });
    },
    submit() {
      this.$emit("submit", this.selectData);
    },
    clickChange(row) {
      console.log(row);
      this.selectData = row;
    },
    queryNewProject() {
      if (this.type == 1) {
        queryNewProject({ controlType: "1" }).then((res) => {
          this.projectList = res.data;
        });
      }
    },
    queryCompanyInfo() {
      getDicts("company_type").then((res) => {
        const guaranteeCodes = res.data
          .filter((item) => item.dictLabel.includes("担保公司"))
          .map((item) => item.dictCode);

        queryCompanyInfo({ companyTypeCodeList: guaranteeCodes.join(','),companyType:this.type==2?4:1 }).then(
          (res) => {
            this.companyList = res.data;
          }
        );
      });
    },
    getList() {
      queryProjectDetails({ ...this.params,isControlFlag:1 }).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    getList2() {
      console.log("123");
      if(!this.params.companyId){
        this.$message.warning("请选择担保公司")
        return
      }
      getOldProjectLoanInfo({ ...this.params }).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    search() {
      if (this.type == 1) {
        this.getList();
      } else {
        this.getList2();
      }
    },
    reset() {
      if (this.type == 1) {
        this.params = {
          controlType: "1",
          pageNum: 1,
          pageSize: 10,
          projectId: "",
          guaranteeCompanyId: "",
        };
        this.getList();
      } else {
        this.params = {
          pageNum: 1,
          pageSize: 10,
          controlType: "1",
          projectId: "",
          companyId: "",
        };
        this.getList2();
      }
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
</style>