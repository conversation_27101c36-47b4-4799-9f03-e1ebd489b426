<template>
  <div>
    <div class="p-5">
      <div style="color: #666">
        本页面展示对每日收款记录点击[生成凭证]后，所生成的记账凭证
      </div>
      <div style="margin-top: 16px">
        <span style="margin-right: 9px">月份</span>
        <el-select
          v-model="billDate"
          @change="getList()"
          placeholder="请选择月份"
          style="margin-right: 9px"
        >
          <el-option
            v-for="item in monthList"
            :key="item.billDate"
            :label="item.billDate"
            :value="item.billDate"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="solid"></div>
    <div class="p-5">
      <div>已生成凭证数：{{ tableData.length }}</div>
      <el-table
        border
        v-if="tableData.length > 0 && forList"
        :data="tableData"
        style="width: 100%; margin-top: 16px"
      >
        <el-table-column align="center" label="操作" width="180" fixed="left">
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['collection:del']"
              type="text"
              @click="del(scope.row)"
              >撤销凭证</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="collection_date"
          label="收款日期"
          width="150"
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="proof_create_time"
          label="凭证生成日期"
          width="280"
        >
        </el-table-column>
        <el-table-column align="center" prop="createBy" label="制单人" width="120">
          <template slot-scope="scope">
            <div>郑军荣</div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="day_abstract"
          label="摘要"
          width="130"
        >
        </el-table-column>
        <el-table-column align="center" prop="cash" label="应收帐款-中信大厦">
          <template slot-scope="scope">
            {{ formatAmount(scope.row.yszk_zhongxin) }}
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :prop="item.name"
          v-for="(item, index) in forList"
          :key="index"
          :label="item.name"
          width="140"
        >
          <template slot-scope="scope">
            {{ formatAmount(scope.row[item.name]) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="cash" label="现金" width="130">
          <template slot-scope="scope">
            {{ formatAmount(scope.row.cash) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="cash" label="银行存款-史总卡到账">
          <template slot-scope="scope">
            {{ formatAmount(scope.row.yhck_szkdz) }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="cash"
          label="银行存款-民生银行POS"
        >
          <template slot-scope="scope">
            {{ formatAmount(scope.row.yhck_msyhPOS) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="cash" label="财务费用-手续费">
          <template slot-scope="scope">
            {{ formatAmount(scope.row.cwfy_sxf) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="cash" label="借方合计">
          <template slot-scope="scope">
            {{ formatAmount(scope.row.jfMoney) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="cash" label="饭卡收入">
          <template slot-scope="scope">
            {{ formatAmount(scope.row.fanka_total) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="cash" label="餐费收入">
          <template slot-scope="scope">
            {{ formatAmount(scope.row.canfei_total) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="cash" label="贷方合计">
          <template slot-scope="scope">
            {{ formatAmount(scope.row.canfei_total + scope.row.fanka_total) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import {
  queryMonthCheck,
  dayVoucharList,
  checkVoucharStatus,
  removeVouchar,
} from "@/api/lyxManage/dailyLedger";
export default {
  data() {
    return {
      tableData: [],
      billDate: "",
      monthList: [],
      forList: [],
    };
  },
  mounted() {
    this.getQueryMonthCheck();
  },
  methods: {
    del(row) {
      checkVoucharStatus({ voucherId: row.voucher_Id }).then((res) => {
        if (!res.isok) {
          this.$confirm(
            "在[智慧财务系统]中该月份已结账，无法撤销记账凭证",
            "撤销失败！",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {})
            .catch(() => {});
        } else {
          this.$confirm(
            "是否撤销该提现记录已生成的记账凭证？本操作将撤销在[智慧财务系统]中已生成的记账凭证撤销后，该提现记录将允许编辑修改，可以重新提交生成记账凭证",
            "撤销记账凭证",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              removeVouchar({
                voucherId: row.voucher_Id,
                id: row.id,
                dayCheckId: 1,
              }).then((res) => {
                this.$message.success("撤销记账凭证成功");
                this.getList();
              });
            })
            .catch(() => {});
        }
      });
    },
    getQueryMonthCheck() {
      queryMonthCheck().then((res) => {
        this.monthList = res;
        this.billDate = this.monthList[this.monthList.length - 1].billDate;

        this.getList();
      });
    },
    getList() {
      dayVoucharList({ selectMonth: this.billDate }).then((res) => {
        this.tableData = res.dataList;
        let arr = [];
        this.tableData.forEach((item) => {
          item.jfMoney = 0;

          item.jfMoney +=
            item.yszk_zhongxin +
            item.cash +
            item.yhck_szkdz +
            item.yhck_msyhPOS +
            item.cwfy_sxf;
          if (item.otherFieldLid && item.otherFieldLid.length > 0) {
            item.otherFieldLid.forEach((i) => {
              item.jfMoney += i.avalues;
            });
            arr = [...arr, ...item.otherFieldLid];
            arr = arr.filter((item, index, self) => {
              return index === self.findIndex((t) => t.name === item.name);
            });
          }
        });

        this.forList = [...arr];
      });
    },
    formatAmount(a, b) {
      if (a == "" || a == "undefined" || a == null || typeof a !== "number") {
        return "";
      }
      var c = "number" == typeof b && b > 0 && 20 >= b ? b : 2;
      a = parseFloat((a + "").replace(/[^\d.-]/g, "")).toFixed(c) + "";
      for (
        var d = a.split(".")[0].split("").reverse(),
          e = a.split(".")[1],
          f = "",
          g = 0;
        g < d.length;
        g++
      )
        f += d[g] + ((g + 1) % 3 == 0 && g + 1 != d.length ? "," : "");
      var re = f.split("").reverse().join("") + "." + e;
      if (re.substr(0, 2) == "-,") {
        return re.replace(",", "");
      } else {
        return re;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.solid {
  width: 100%;
  height: 10px;
  background: #f8f8f9;
}
</style>