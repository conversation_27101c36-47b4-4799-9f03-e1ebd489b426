<template>
  <div>
    <div class="pb-28">
      <el-form ref="form" :model="myform" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="岗位名称:" prop="postName">
              <el-input
                v-model="myform.postName"
                placeholder="请输入岗位名称"
                class="w-1/2"
                :disabled="Boolean(id) && myform.postType == 1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="岗位编码:" prop="postCode">
              <el-input
                v-model="myform.postCode"
                placeholder="请输入编码名称"
                class="w-1/2"
                :disabled="Boolean(id) && myform.postType == 1"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="所属部门:" prop="deptId">
              <el-input
                v-if="myform.dept && myform.dept.deptName"
                v-model="myform.dept.deptName"
                readonly
                class="w-1/2 mr-3"
              ></el-input>
              <el-button type="primary" size="small" @click="selectDep"
                >选择部门</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="岗位负责人:">
              <el-input
                v-if="myform.leaderName"
                v-model="myform.leaderName"
                readonly
                class="w-1/2 mr-3"
              ></el-input>
              <el-button type="primary" size="small" @click="selectPost"
                >选择岗位</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="岗位类型:" prop="postType">
              <el-select
                v-model="myform.postType"
                placeholder="岗位类型"
                clearable
                size="small"
                :disabled="Boolean(id)"
              >
                <el-option
                  v-for="dict in dict.type.sys_post_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              <div
                v-show="myform.postType == 1"
                style="color: rgb(153, 153, 153)"
              >
                系统岗位在创建后名称与编码不能被修改
              </div>
              <div
                v-show="myform.postType == 2"
                style="color: rgb(153, 153, 153)"
              >
                自定义岗位在创建后，岗位名称、岗位编码可修改
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="岗位顺序:">
              <el-input-number
                v-model="myform.postSort"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态:">
              <el-radio-group v-model="myform.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                v-model="myform.remark"
                type="textarea"
                placeholder="请输入内容"
                class="w-1/2"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div>
        <div class="font-bold mb-5">功能菜单权限</div>
        <div class="my-2">
          <span class="font-bold text-sm">复制岗位菜单权限</span>
          <el-select
            class="mx-2"
            v-model="copyPost"
            filterable
            placeholder="选择您要复制菜单的岗位"
          >
            <el-option
              v-for="item in postOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-button type="primary" plain size="mini" @click="copyMenu"
            >复制菜单</el-button
          >
        </div>

        <el-checkbox
          v-model="menuExpand"
          @change="handleCheckedTreeExpand($event)"
          >展开/折叠</el-checkbox
        >
        <el-checkbox
          v-model="menuNodeAll"
          @change="handleCheckedTreeNodeAll($event)"
          >全选/全不选</el-checkbox
        >
        <el-checkbox
          v-model="myform.menuCheckStrictly"
          @change="handleCheckedTreeConnect($event)"
          >父子联动</el-checkbox
        >
        <el-tree
          class="tree-border mb-5"
          :data="menuOptions"
          show-checkbox
          ref="menu"
          node-key="id"
          :check-strictly="!myform.menuCheckStrictly"
          empty-text="加载中，请稍候"
          :props="defaultProps"
        ></el-tree>
      </div>
    </div>
    <div
      class="flex justify-center fixed bottom-0 bg-white z-10 w-full pb-2"
      style="left: 130px"
    >
      <el-button type="primary" class="mr-5" @click="submitForm"
        >确定</el-button
      >
      <el-button @click="cancel">取消</el-button>
    </div>
    <UserDepPostSelect
      :title="title"
      :multiple="false"
      :rowKey="rowKey"
      :multipleSelectionProp="multipleSelectionDepPost"
      v-model="openUserDepPost"
      @on-submit-success-dep="depSuccess"
      @on-submit-success-post="postSuccess"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import config from "./config";
import { addPost, updatePost, fastListPost } from "@/api/system/post";
import {
  postMenuTreeSelect,
  treeselect as menuTreeselect,
} from "@/api/system/menu";
import { filterTreeData } from "@/utils";

export default {
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    type: {
      type: [String, Number],
      required: true,
      default: "",
    },
    id: {
      type: [String, Number],
      required: true,
      default: "",
    },
  },
  dicts: ["sys_normal_disable", "sys_post_type"],
  data() {
    return {
      ...config,
      myform: {},
      openUserDepPost: false,
      title: "",
      menuOptions: [],
      menuExpand: false,
      menuNodeAll: false,
      multipleSelectionDepPost: [],
      rowKey: "deptId",
      checkedKeysInit: [],
      checkedKeysInitArr: [],
      copyPost: "",
      postOptions: [],
    };
  },
  watch: {
    form: {
      handler(val) {
        this.myform = XEUtils.clone(val, true);
        //默认父子联动勾选
        this.$set(this.myform, "menuCheckStrictly", true);
      },
      immediate: true,
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      if (this.type == "add") {
        this.myform.status = "0";
        this.myform.postSort = "0";
        this.myform.dept = {};
        this.getMenuTreeselect();
      } else {
        this.getPost(this.id);
      }
      //默认父子联动勾选
      this.$set(this.myform, "menuCheckStrictly", true);
      this.getPostList();
    },
    getPostList() {
      fastListPost({}).then((res) => {
        this.postOptions = res.rows.map((item) => ({
          value: item.postId,
            label: item.postName,
          }))
          .filter((item) => item.value != this.id);
      });
    },
    copyMenu() {
      if (this.copyPost) {
        this.$alert(
          "请您确认是否复制该岗位的菜单权限。<br/>复制后，当前岗位的菜单权限与被复制岗位将保持一致！",
          "提示",
          {
            dangerouslyUseHTMLString: true,
          }
        )
          .then(() => {
            return this.getPost(this.copyPost);
          })
          .then(() => {
            this.$modal.msgSuccess("复制成功");
          })
          .catch(() => {});
      } else {
        this.$modal.msgWarning("请您选择要复制的岗位");
      }
    },
    selectDep() {
      this.title = "dep";
      this.rowKey = "deptId";
      if (this.myform.deptId) {
        this.setMultipleSelectionDepPost([{ deptId: this.myform.deptId }]);
      } else {
        this.setMultipleSelectionDepPost([]);
      }
      this.openUserDepPost = true;
    },
    selectPost() {
      this.title = "post";
      this.rowKey = "postId";
      if (this.myform.postId) {
        this.setMultipleSelectionDepPost([{ postId: this.myform.postId }]);
      } else {
        this.setMultipleSelectionDepPost([]);
      }
      this.openUserDepPost = true;
    },
    depSuccess(value) {
      this.myform.dept.deptName = value[0].deptName;
      this.$set(this.myform, "deptId", value[0].deptId);
      this.myform.unitId = value[0].unitId;
      this.$refs.form.clearValidate();
    },
    postSuccess(value) {
      this.myform.leader = value[0].postId;
      this.myform.leaderName = value[0].postName;
    },
    setMultipleSelectionDepPost(arr) {
      this.multipleSelectionDepPost = arr;
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect().then((response) => {
        this.menuOptions = response.data;
      });
    },
    /** 根据岗位ID查询菜单树结构 */
    getPostMenuTreeselect(postId) {
      return postMenuTreeSelect(postId).then((response) => {
        this.menuOptions = response.menus;
        this.checkedKeysInitArr = XEUtils.toTreeArray(response.menus, {
          children: "children",
        }).map((item) => item.id);
        return response;
      });
    },
    async getPost(id) {
      const roleMenu = this.getPostMenuTreeselect(id);
      this.$nextTick(() => {
        roleMenu.then((res) => {
          let checkedKeys = res.checkedKeys;
          this.checkedKeysInit = res.checkedKeys.filter((item) =>
            this.checkedKeysInitArr.includes(item)
          );
          checkedKeys.forEach((v) => {
            this.$nextTick(() => {
              console.log(v);
              this.$refs.menu.setChecked(v, true, false);
            });
          });
        });
      });
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      let checkedKeys = this.$refs.menu.getCheckedKeys();
      // 半选中的菜单节点
      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value) {
      let treeList = this.menuOptions;
      for (let i = 0; i < treeList.length; i++) {
        this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value) {
      this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value) {
      this.myform.menuCheckStrictly = value ? true : false;
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.myform.menuIds = this.getMenuAllCheckedKeys().map((item) => {
            return {
              menuId: item,
            };
          });
          if (this.type != "add") {
            const newIds = this.myform.menuIds.map((item) => item.menuId);
            const delMenuList =
              this.checkedKeysInit.filter((item) => !newIds.includes(item)) ||
              [];
            const addMenuList =
              newIds.filter((item) => !this.checkedKeysInit.includes(item)) ||
              [];
            this.myform.delMenuList = delMenuList;
            this.myform.addMenuList = addMenuList;
            updatePost(this.myform).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.$parent.addUpdateCallBack();
            });
          } else {
            addPost(this.myform).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.$parent.addUpdateCallBack();
            });
          }
        }
      });
    },
    cancel() {
      this.$parent.addUpdateCallBack();
    },
  },
};
</script>
