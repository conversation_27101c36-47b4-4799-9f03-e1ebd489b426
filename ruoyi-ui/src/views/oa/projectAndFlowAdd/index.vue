<template>
  <div id="projectAndFlowAdd">
    <div v-if="!nextType">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        style="padding-left: 44px"
        label-width="82px"
      >
        <div style="width: 100%; height: 30px"></div>
        <el-form-item label="公司" prop="compony">
          <el-select
            v-model="queryParams.companyNo"
            size="mini"
            filterable
            style="width: 300px"
            placeholder="请选择公司"
            @change="updatecompany($event)"
          >
            <el-option
              v-for="item in projects"
              :key="item.unitId"
              :label="item.unitShortName"
              :value="item.unitId"
            ></el-option>
          </el-select>
        </el-form-item>
        <br />
        <el-form-item v-show="modelshow" label="流程模板" prop="template">
          <el-select
            v-model="queryParams.modelId"
            size="mini"
            filterable
            style="width: 300px"
            placeholder="请选择流程模板"
            @change="updatemodel($event)"
          >
            <el-option
              v-for="item in modelList"
              :key="item.parentId"
              :label="item.templateName"
              :value="item.parentId"
            ></el-option>
          </el-select>
        </el-form-item>
        <br />
        <el-form-item
          style="margin-right: 245px"
          v-show="modelshow"
          label="联动财务项目系统"
          prop="template"
        >
          <el-switch v-model="queryParams.isLinkageCwxmgl"></el-switch>
          <span style="margin-left: 10px">
            {{
            queryParams.isLinkageCwxmgl ? "是" : "否"
            }}
          </span>
        </el-form-item>
        <p
          style="
            width: 500px;
            text-align: left;
            color: #999;
            margin-left: 87px;
            margin-bottom: 0;
          "
        >启用后，当流程结束时会在[财务项目管理]系统对应的项目中同步已支付返费金额，数值等于流程中用户在[记账金额字段]填写的金额</p>
        <br />
        <el-form-item v-show="queryParams.isLinkageCwxmgl" label="记账金额字段" prop="accountingField">
          <el-select
            v-model="queryParams.accountingField"
            size="mini"
            filterable
            @change="changeAcc"
            style="width: 300px"
            placeholder="请选择记账金额字段"
          >
            <el-option
              v-for="item in flowList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryParams.isLinkageCwxmgl" label="项目字段" prop="projectField">
          <el-select
            v-model="queryParams.projectField"
            size="mini"
            filterable
            style="width: 300px"
            placeholder="请选择项目字段"
          >
            <el-option
              v-for="item in flowList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <br />
        <el-form-item v-show="queryParams.isLinkageCwxmgl" label="项目类型" prop="projectTypeField">
          <el-select
            v-model="queryParams.projectTypeField"
            size="mini"
            filterable
            @change="changeAcc2"
            style="width: 300px"
            placeholder="请选择项目类型字段"
          >
            <el-option
              v-for="item in flowList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryParams.isLinkageCwxmgl" label="返费公司" prop="feeCompanyField">
          <el-select
            v-model="queryParams.feeCompanyField"
            size="mini"
            @change="changeAcc3"
            filterable
            style="width: 300px"
            placeholder="请选择信息费公司字段"
          >
            <el-option
              v-for="item in flowList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div v-if="changeEditType == 'true'">
        <el-divider></el-divider>
        <p style="padding-left: 214px">管理员已开启编辑需审核功能</p>
        <p style="padding-left: 214px">
          如果项目与流程关联规则发生修改，必须由财务责任人、业务责任人审核确认后才能生效
          <span style="color: #409eff">规则说明</span>
          <el-tooltip placement="top">
            <div slot="content">
              管理员已开启编辑需审核功能
              <br />所有编辑操作必须设置了财务、业务责任人后才能提交
              <br />财务责任人提交编辑修改后，需要业务责任人审核后，本次编辑才能生效
              <br />业务责任人提交编辑修改后，需要财务责任人审核后，本次编辑才能生效
              <br />审核可以被驳回，驳回后本次编辑无效
              <br />审核中的规则，将锁定无法被编辑
              <br />审核通过后新规则即时生效
              <br />可指定任任何属于财务角色的人员，作为财务责任人。如果您是财务角色，但还不是财务责任人，您提交修改后将自动成为本规则的财务责任人
              <br />可指定任任何属于财务角色的人员，作为业务责任人。如果您是业务角色，但还不是业务责任人，您提交修改后将自动成为本规则的业务责任人
              <br />
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <div class="item">
          <span>
            <i>*</i>财务责任人
          </span>
          <el-select
            v-model="queryParams.salesmanList"
            size="mini"
            placeholder="请选择"
            filterable
            multiple
          >
            <el-option
              v-for="item in userList.caiwu"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <p
            v-if="
              caiwuType &&
              !queryParams.salesmanList.includes(userId) &&
              !roleList.includes('caiwuAdmin')
            "
            style="margin-left: 210px; margin-bottom: 0"
          >当前您不是本项目与流程关联规则的财务责任人</p>
          <p
            v-if="
              caiwuType &&
              !queryParams.salesmanList.includes(userId) &&
              !roleList.includes('caiwuAdmin')
            "
            style="margin-left: 210px; margin-bottom: 0"
          >提交修改后，您将自动成为该项目与流程关联规则的财务责任人</p>
        </div>
        <div class="item">
          <span>
            <i>*</i>业务责任人
          </span>
          <el-select
            v-model="queryParams.financialStaffList"
            size="mini"
            placeholder="请选择"
            filterable
            multiple
          >
            <el-option
              v-for="item in userList.yewu"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <p
            v-if="
              yewuType &&
              !queryParams.financialStaffList.includes(userId) &&
              !roleList.includes('yewuAdmin')
            "
            style="margin-left: 210px; margin-bottom: 0"
          >当前您不是本项目与流程关联规则的业务责任人</p>
          <p
            v-if="
              yewuType &&
              !queryParams.financialStaffList.includes(userId) &&
              !roleList.includes('yewuAdmin')
            "
            style="margin-left: 210px; margin-bottom: 0"
          >提交修改后，您将自动成为该项目与流程关联规则的业务责任人</p>
        </div>
      </div>
      <div v-show="projectshow">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleProAdd">添加项目</el-button>
      </div>
      <div v-show="projectshow" class="container" v-for="(item, index) in proList" :key="index">
        <el-form :model="item" :inline="true" label-width="82px" :ref="'dataForm' + index">
          <el-form-item label="项目名称" prop="project">
            <el-select
              v-model="item.projectId"
              size="mini"
              filterable
              style="width: 180px"
              @change="updateproject($event, index)"
              placeholder="请选择项目名称"
            >
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.projectName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <el-table v-show="payershow" :data="item.tableList" border>
          <el-table-column label="收款人" align="center" prop="collBankOfDeposit">
            <template slot-scope="scope">
              <el-form
                :model="scope.row"
                :inline="true"
                label-width="82px"
                :ref="'prod' + index + 'formpayee' + scope.$index"
              >
                <el-select
                  v-model="scope.row.collId"
                  clearable
                  size="mini"
                  filterable
                  style="width: 180px"
                  @change="updateindexpeo($event, index, scope.$index)"
                  placeholder="请选择收款人"
                >
                  <el-option
                    v-for="item in payeeList"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  />
                </el-select>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column
            label="收款人开户行"
            align="center"
            prop="collBankOfDeposit"
            show-overflow-tooltip
          />
          <el-table-column
            label="收款人账号"
            align="center"
            prop="collAccountNumber"
            show-overflow-tooltip
          />
          <el-table-column
            label="收款人简称"
            align="center"
            prop="collAbbreviation"
            show-overflow-tooltip
          />
          <el-table-column label="付款人" align="center" prop="name1">
            <template slot-scope="scope">
              <el-form
                :model="scope.row"
                :inline="true"
                label-width="82px"
                :ref="'prod' + index + 'formpayer' + scope.$index"
              >
                <el-select
                  v-model="scope.row.payId"
                  clearable
                  size="mini"
                  filterable
                  style="width: 180px"
                  @change="updateindexpay($event, index, scope.$index)"
                  placeholder="请选择付款人"
                >
                  <el-option
                    v-for="item in payerList"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  />
                </el-select>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column
            label="付款人开户行"
            align="center"
            prop="payBankOfDeposit"
            show-overflow-tooltip
          />
          <el-table-column
            label="付款人账号"
            align="center"
            prop="payAccountNumber"
            show-overflow-tooltip
          />
          <el-table-column
            label="付款人简称"
            align="center"
            prop="payAbbreviation"
            show-overflow-tooltip
          />
          <el-table-column fixed="right" align="center" label="操作" width="100">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleDel(index, scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-show="payershow"
          @click="handleAdd(index)"
          style="margin-top: 10px"
        >添加收款人/付款人</el-button>
        <!-- <el-button
        type="danger"
        icon="el-icon-minus"
        size="mini"
        v-show="payershow"
        @click="handleproRemove(index)"
        style="margin-top: 10px"
        >删除项目</el-button
        >-->
      </div>
      <el-form
        v-show="projectshow"
        class="dataForm"
        ref="dataForm"
        :model="dataForm"
        label-width="110px"
        inline
      >
        <el-form-item style="margin-left: 35%" label="备注" prop="remark">
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 10 }"
            v-model.trim="dataForm.remark"
            placeholder="请输入备注"
            style="width: 520px"
            clearable
          ></el-input>
        </el-form-item>
        <br />
        <el-form-item v-if="changeEditType == 'false'"></el-form-item>
      </el-form>
      <div style="padding-left: 16px">
        <el-button
          type="primary"
          style="background-color: #ff9900; border: none"
          size="mini"
          @click="delItem"
          v-if="projectId"
        >删除项目与流程关联规则</el-button>
        <el-button size="mini" @click="handleCel">取消</el-button>
        <el-button
          :disabled="buttonisuse"
          type="primary"
          size="mini"
          @click="opendeolog"
          v-if="
            changeEditType == 'false' ||
            (changeEditType == 'true' && !projectId)
          "
        >保存</el-button>
        <el-button
          :disabled="buttonisuse"
          type="primary"
          size="mini"
          @click="next"
          v-if="changeEditType == 'true' && !nextType && projectId"
        >下一步</el-button>
      </div>
    </div>
    <div v-else>
      <editData
        :projects="projects"
        :personData="personData"
        @shangyibu="handleCel"
        @nextSubmit="nextSubmit"
        :oldData="oldData"
        :newData="queryParams"
      />
    </div>

    <el-dialog :visible.sync="updateOrAddDeilog" width="30%">
      <span>是否确定保存此项目与流程的关联信息？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cencelclose()">取 消</el-button>
        <el-button type="primary" @click="handleSave()">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="editSubmitType"
      width="500px"
      :show-close="false"
    >
      <p style="font-weight: bold; text-align: center">编辑申请已提交!</p>
      <p style="text-align: center">以下人员将在OA系统待办中收到待审核通知，审核通过后编辑内容立即生效。请及时沟通以尽快完成审核</p>
      <p style="text-align: center">
        <span style="font-weight: bold">{{ personData.zrr }}</span>
        ：{{ personData.zrrList }}
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="delSubmitType"
      width="500px"
      :show-close="false"
    >
      <p style="font-weight: bold; text-align: center">删除申请已提交!</p>
      <p style="text-align: center">以下人员将在OA系统待办中收到待审核通知，审核通过后删除立即生效。请及时沟通以尽快完成审核</p>
      <p style="text-align: center">
        <span style="font-weight: bold">{{ personData.zrr }}</span>
        ：{{ personData.zrrList }}
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </span>
    </el-dialog>
    <delItem
      v-if="delItemType"
      :personData="personData"
      @close="delItemType = false"
      @submit="delSubmit"
    />
  </div>
</template>
<script>
import delItem from "./delItem.vue";

import editData from "./editData.vue";
import {
  getuser,
  addNewEditInfoProject,
  projectDetail,
  projectCheck,
  projectConfirm
} from "@/api/oa/voucharRules";
import { queryFormFeild } from "@/api/form/form";
import { getSelectData } from "@/api/oa/trader";
import { listDeploy } from "@/api/oa/deploy";
import { addproMain, getupdataData, deleteMain } from "@/api/oa/main";
import { getUnitListEnable } from "@/api/system/unit";

import { getDataByRoleAndCompanyIdFinance } from "@/api/oa/processTemplate";
export default {
  components: {
    editData,
    delItem
  },
  name: "ProjectAndFlowAdd",
  data() {
    return {
      delItemType: false,
      editSubmitType: false,
      delSubmitType: false,
      nextType: false,
      flowList: [],
      projectId: "",
      buttonisuse: true,
      projectshow: false,
      modelshow: false,
      payershow: false,
      updateOrAddDeilog: false,
      //公司下拉框数据
      projects: [],
      //流程模板数据
      modelList: [],
      queryParams: {
        id: "",
        compony: "",
        companyNo: "",
        projectField: "",
        accountingField: "",
        financialStaffList: [],
        salesmanList: [],
        accountingFieldName: "",
        projectTypeField: "",
        projectTypeFieldName: "",
        feeCompanyField: "",
        feeCompanyFieldName: "",
        isLinkageCwxmgl: false,
        modelName: "",
        modelId: ""
      },
      proList: [
        {
          projectName: "",
          projectId: "",
          tableList: [
            {
              collId: "",
              collName: "",
              collBankOfDeposit: "",
              collAccountNumber: "",
              collAbbreviation: "",
              payName: "",
              payBankOfDeposit: "",
              payAccountNumber: "",
              payAbbreviation: "",
              payId: ""
            }
          ]
        }
      ],
      projectList: [],
      payeeList: [],
      payerList: [],
      dataForm: {
        remark: ""
      },
      editType: false,
      changeEditType: "",
      userList: {},
      yewuType: false,
      caiwuType: false,
      roleList: [],
      userId: "",
      personData: {},
      newData: null,
      oldData: null
    };
  },
  created() {
    this.changeEditType = this.$route.query.changeEditType;
    this.userId = Number(sessionStorage.getItem("userId"));
    if (sessionStorage.getItem("roleList")) {
      this.roleList = JSON.parse(sessionStorage.getItem("roleList"));
    }

    this.getSelect();
    this.getDataById();
  },
  methods: {
    delSubmit(e) {
      let data = {
        ...this.queryParams,
        proList: this.proList,
        ...this.dataForm,
        editInfo: e,
        editType: 2,
        oaApplyRecordsOldId: this.queryParams.oaApplyRecordsOldId
      };
      addNewEditInfoProject(data).then(res => {
        if (res.code == 200) {
          this.delSubmitType = true;
        }
      });
    },
    delItem() {
      if (this.changeEditType == "true") {
        this.personData = {
          tjsfr: this.yewuType
            ? "业务责任人"
            : this.caiwuType
            ? "财务责任人"
            : this.roleList.includes("caiwuAdmin")
            ? "财务管理员"
            : this.roleList.includes("yewuAdmin")
            ? "业务管理员"
            : ""
        };
        if (this.yewuType) {
          let list = [];
          this.userList.caiwu.forEach(item => {
            this.queryParams.salesmanList.forEach(i => {
              if (item.value == i) {
                list.push(item);
              }
            });
          });
          this.personData.zrr = "财务责任人";
          this.personData.zrrList = list.map(item => item.label);
        }
        if (this.caiwuType) {
          let list = [];
          this.userList.yewu.forEach(item => {
            this.queryParams.financialStaffList.forEach(i => {
              if (item.value == i) {
                list.push(item);
              }
            });
          });
          this.personData.zrr = "业务责任人";
          this.personData.zrrList = list.map(item => item.label);
        }
        this.delItemType = true;
      } else {
        this.$confirm("是否确认删除该规则?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            deleteMain(this.projectId).then(res => {
              this.$modal.msgSuccess("删除配置成功！");
              let route = {
                fullPath: "/oa/projectAndFlow",
                name: "ProjectAndFlow",
                path: "/oa/projectAndFlow"
              };
              this.closeSelectedTag(route);
              setTimeout(() => {
                this.$router.push({ path: "/oa/projectAndFlow" });
              }, 500);
            });
          })
          .catch(() => {});
      }
    },
    nextSubmit(e) {
      addNewEditInfoProject({ ...e, ...this.dataForm }).then(res => {
        if (res.code == 200) {
          this.editSubmitType = true;
        }
      });
    },
    next() {
      if (
        this.$route.query.changeEditType == "true" &&
        this.queryParams.salesmanList.length == 0
      ) {
        this.$message.warning("请选择财务负责人");
        return;
      }
      if (
        this.$route.query.changeEditType == "true" &&
        this.queryParams.financialStaffList.length == 0
      ) {
        this.$message.warning("请选择业务负责人");
        return;
      }
      if (
        this.queryParams.isLinkageCwxmgl &&
        !this.queryParams.accountingField
      ) {
        this.$message.warning("请填写完整表单");
        return;
      }
      this.personData = {
        tjsfr: this.yewuType
          ? "业务责任人"
          : this.caiwuType
          ? "财务责任人"
          : this.roleList.includes("caiwuAdmin")
          ? "财务管理员"
          : this.roleList.includes("yewuAdmin")
          ? "业务管理员"
          : ""
      };
      if (this.yewuType) {
        let list = [];
        this.userList.caiwu.forEach(item => {
          this.queryParams.salesmanList.forEach(i => {
            if (item.value == i) {
              list.push(item);
            }
          });
        });
        this.personData.zrr = "财务责任人";
        this.personData.zrrList = list.map(item => item.label);
      }
      if (this.caiwuType) {
        let list = [];
        this.userList.yewu.forEach(item => {
          this.queryParams.financialStaffList.forEach(i => {
            if (item.value == i) {
              list.push(item);
            }
          });
        });
        this.personData.zrr = "业务责任人";
        this.personData.zrrList = list.map(item => item.label);
      }
      this.nextType = true;
    },
    getUser2() {
      getuser().then(res => {
        this.userList = res;
        let yewu = this.userList.yewu.find(item => {
          return item.value == this.userId;
        });
        if (yewu) {
          this.yewuType = true;
          if (this.queryParams.financialStaffList.includes(yewu.value)) {
            return;
          }
          this.queryParams.financialStaffList.push(yewu.value);
          return;
        }
        let caiwu = this.userList.caiwu.find(item => {
          return item.value == this.userId;
        });
        if (caiwu) {
          this.caiwuType = true;
          if (this.queryParams.salesmanList.includes(caiwu.value)) {
            return;
          }
          this.queryParams.salesmanList.push(caiwu.value);
        }
      });
    },
    changeAcc(e) {
      let data = this.flowList.find(item => {
        return item.value == e;
      });
      this.queryParams.accountingFieldName = data.label;
    },
    changeAcc2(e) {
      let data = this.flowList.find(item => {
        return item.value == e;
      });
      this.queryParams.projectTypeFieldName = data.label;
    },
    changeAcc3(e) {
      let data = this.flowList.find(item => {
        return item.value == e;
      });
      this.queryParams.feeCompanyFieldName = data.label;
    },
    async getDataById() {
      this.projectId = this.$route.query.mainId;
      console.log(this.projectId);
      if (this.projectId != "") {
        await getupdataData(this.projectId).then(response => {
          this.queryParams.id = this.projectId;
          this.queryParams.salesmanList = response.data.salesmanList;
          this.queryParams.financialStaffList =
            response.data.financialStaffList;
          this.getUser2();
          this.queryParams.accountingField =
            response.data.accountingField || null;
          this.queryParams.projectTypeField =
            response.data.projectTypeField || null;
          this.queryParams.projectTypeFieldName =
            response.data.projectTypeFieldName || null;
          this.queryParams.feeCompanyField =
            response.data.feeCompanyField || null;
          this.queryParams.feeCompanyFieldName =
            response.data.feeCompanyFieldName || null;
          this.queryParams.accountingFieldName =
            response.data.accountingFieldName || null;
          this.queryParams.companyNo = response.data.companyNo;
          this.queryParams.isLinkageCwxmgl =
            response.data.isLinkageCwxmgl == "Y" ? true : false;
          this.queryParams.modelName = response.data.modelName;
          this.queryParams.modelId = response.data.modelId;
          this.queryParams.projectField = response.data.projectField || null;
          this.queryParams.oaApplyRecordsOldId =
            response.data.oaApplyRecordsOldId;
          this.proList = response.data.proList;
          this.queryParams.proList = response.data.proList;
          this.oldData = JSON.parse(JSON.stringify(this.queryParams));

          this.buttonisuse = false;
          this.projectshow = true;
          this.modelshow = true;
          this.payershow = true;
          this.dataForm.remark = response.data.remark;
          var data = {
            classificationId: this.queryParams.companyNo
          };
          console.log(data);
          //根据公司查询模板和账套
          getDataByRoleAndCompanyIdFinance(data).then(response => {
            this.modelList = response.rows;
            let a = this.modelList.find(item => {
              return item.parentId == this.queryParams.modelId;
            });
            if (a) {
              queryFormFeild({ formId: a.formId }).then(res => {
                this.flowList = res;
              });
            }
          });
        });
      } else {
        this.getUser2();
      }

      getUnitListEnable().then(response => {
        this.projects = response.data;
      });

      // noForbiddenList().then(response => {
      //   this.modelList = response.rows;
      // });
    },
    opendeolog() {
      console.log(this.queryParams);
      if (
        this.queryParams.isLinkageCwxmgl &&
        !this.queryParams.accountingField
      ) {
        this.$message.warning("请填写完整表单");
        return;
      }
      this.handleSave();
    },
    cencelclose() {
      this.updateOrAddDeilog = false;
    },
    //获取下拉框数据
    getSelect() {
      var fukuan = {
        traderType: 0,
        isEnable: "Y"
      };
      //获取付款人
      getSelectData(fukuan).then(response => {
        this.payerList = response;
      });
      var shoukuan = {
        traderType: 1,
        isEnable: "Y"
      };
      //获取收款人
      getSelectData(shoukuan).then(response => {
        this.payeeList = response;
      });
      //获取项目名称
      listDeploy({}).then(response => {
        this.projectList = response.rows;
      });
    },
    handleProAdd() {
      const info = {
        projectName: "",
        tableList: [
          {
            collId: "",
            collName: "",
            collBankOfDeposit: "",
            collAccountNumber: "",
            collAbbreviation: "",
            payName: "",
            payBankOfDeposit: "",
            payAccountNumber: "",
            payAbbreviation: "",
            payId: ""
          }
        ]
      };
      this.proList.push(info);
    },
    handleproRemove(index) {
      if (this.proList.length > 1) {
        this.proList.splice(index, 1);
      }
    },
    handleDel(idx, index) {
      if (this.proList[idx].tableList.length > 1) {
        this.proList[idx].tableList.splice(index, 1);
      }
    },
    handleAdd(index) {
      const info = {
        collId: "",
        collName: "",
        collBankOfDeposit: "",
        collAccountNumber: "",
        collAbbreviation: "",
        payName: "",
        payBankOfDeposit: "",
        payAccountNumber: "",
        payAbbreviation: "",
        payId: ""
      };
      this.proList[index].tableList.push(info);
    },
    handleCel() {
      this.$router.push({ path: "/oa/projectAndFlow" });
    },
    close() {
      this.editSubmitType = false;
      this.delSubmitType = false;
      this.delItemType = false;
      let route = {
        fullPath: "/oa/projectAndFlow",
        name: "ProjectAndFlow",
        path: "/oa/projectAndFlow"
      };
      this.closeSelectedTag(route);
      setTimeout(() => {
        this.$router.push({ path: "/oa/projectAndFlow" });
      }, 500);
    },
    closeSelectedTag(view) {
      console.log(view);
      this.$tab.closePage(view).then(({ visitedViews }) => {});
    },
    async handleSave() {
      const data = {
        ...this.queryParams,
        proList: this.proList,
        ...this.dataForm
      };
      data.isLinkageCwxmgl = data.isLinkageCwxmgl ? "Y" : "N";
      if (this.changeEditType == "true") {
        addNewEditInfoProject({
          ...data,
          editType: !this.projectId ? "0" : "1"
        }).then(res => {});
      } else {
        addproMain(data).then(response => {});
      }

      this.$router.push({ path: "/oa/projectAndFlow" });
    },
    updateindexpeo(event, proindex, payindex) {
      const list = this.payeeList.filter(item => item.id === event);
      //显示
      this.proList[proindex].tableList[payindex].collBankOfDeposit =
        list[0].bankOfDeposit;
      this.proList[proindex].tableList[payindex].collAccountNumber =
        list[0].accountNumber;
      this.proList[proindex].tableList[payindex].collAbbreviation =
        list[0].abbreviation;
      this.proList[proindex].tableList[payindex].collName = list[0].userName;
      //传值
      if (
        this.proList[proindex].tableList[payindex].collBankOfDeposit != "" &&
        this.proList[proindex].tableList[payindex].payBankOfDeposit != ""
      ) {
        this.buttonisuse = false;
      }
    },
    updateindexpay(event, proindex, payindex) {
      const list = this.payerList.filter(item => item.id === event);
      this.proList[proindex].tableList[payindex].payBankOfDeposit =
        list[0].bankOfDeposit;
      this.proList[proindex].tableList[payindex].payAccountNumber =
        list[0].accountNumber;
      this.proList[proindex].tableList[payindex].payAbbreviation =
        list[0].abbreviation;
      this.proList[proindex].tableList[payindex].payName = list[0].userName;
      if (
        this.proList[proindex].tableList[payindex].collBankOfDeposit != "" &&
        this.proList[proindex].tableList[payindex].payBankOfDeposit != ""
      ) {
        this.buttonisuse = false;
      }
    },
    updatecompany(event) {
      this.modelshow = true;
      const list = this.projects.filter(item => item.unitId === event);
      this.queryParams.compony = list[0].unitShortName;

      var data = {
        classificationId: list[0].unitId
      };
      console.log(data);
      //根据公司查询模板和账套
      getDataByRoleAndCompanyIdFinance(data).then(response => {
        this.modelList = response.rows;
      });
    },
    updatemodel(event) {
      this.projectshow = true;
      const list = this.modelList.filter(item => item.parentId === event);
      console.log(list);
      queryFormFeild({ formId: list[0].formId }).then(res => {
        this.flowList = res;
      });
      this.queryParams.modelName = list[0].templateName;
    },
    updateproject(event, index) {
      this.payershow = true;
      const list = this.projectList.filter(item => item.id === event);
      this.proList[index].projectName = list[0].projectName;
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-form-item--medium .el-form-item__label {
  width: 130px !important;
}
.container {
  padding: 20px;
  margin: 10px 0;
  border: 1px solid #ccc;
}
.dataForm {
  margin-top: 20px;
}
.item {
  margin-bottom: 12px;
  /deep/ .el-input__inner {
    width: 250px !important;
  }
  span {
    display: inline-block;
    width: 200px;
    text-align: right;
    margin-right: 12px;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
</style>
