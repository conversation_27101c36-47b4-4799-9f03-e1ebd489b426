import request from '@/utils/request'

// 查询岗位列表
export function listPost(query) {
  return request({
    url: '/system/post/list',
    method: 'get',
    params: query
  })
}
export function fastListPost(query) {
  return request({
    url: '/system/post/fastList',
    method: 'get',
    params: query
  })
}
export function noPermissionPostList(query) {
  return request({
    url: '/system/post/noPermissionList',
    method: 'get',
    params: query
  })
}
export function getUnit(query) {
  return request({
    url: '/system/information/getUnit',
    method: 'get',
    params: query
  })
}
export function treeselect(query) {
  return request({
      url: '/system/dept/treeselect',
      method: 'get',
      params: query
  })
}
// 查询岗位详细
export function getPost(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  })
}
export function getPost2(query) {
  return request({
    url: '/system/post',
    method: 'get',
    params: query
  })
}

// 新增岗位
export function addPost(data) {
  return request({
    url: '/system/post',
    method: 'post',
    data: data
  })
}

// 修改岗位
export function updatePost(data) {
  return request({
    url: '/system/post',
    method: 'put',
    data: data
  })
}

// 删除岗位
export function delPost(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'delete'
  })
}
// 获取岗位列表,用于下拉选择，所有岗位(含禁用)
export function optionselect() {
  return request({
    url: '/system/post/optionselect',
    method: 'get'
  })
}
// 获取用户岗位公司部门集合
export function userPostSetList() {
  return request({
    url: '/system/post/userPostSetList',
    method: 'get'
  })
}
// 获取岗位公司部门集合
export function postSetList() {
  return request({
    url: '/system/post/postSetList',
    method: 'get'
  })
}
export function batchUserPost(data) {
  return request({
    url: '/system/post/batchUserPost',
    method: 'post',
    data
  })
}