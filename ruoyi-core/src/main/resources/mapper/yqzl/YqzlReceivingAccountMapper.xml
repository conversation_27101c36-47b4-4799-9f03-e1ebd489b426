<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.yqzl.mapper.YqzlReceivingAccountMapper">

    <resultMap type="YqzlReceivingAccount" id="YqzlReceivingAccountResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="recAccountNo"    column="rec_account_no"    />
        <result property="recAccountName"    column="rec_account_name"    />
        <result property="recAccountNumber"    column="rec_account_number"    />
        <result property="recOpenBankName"    column="rec_open_bank_name"    />
        <result property="recOpenBankCode"    column="rec_open_bank_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectYqzlReceivingAccountVo">
        select id, user_id, rec_account_no, rec_account_name, rec_account_number, rec_open_bank_name, rec_open_bank_code, create_by, create_time, update_by, update_time from yqzl_receiving_account
    </sql>

    <select id="selectYqzlReceivingAccountList" parameterType="YqzlReceivingAccount" resultMap="YqzlReceivingAccountResult">
        <include refid="selectYqzlReceivingAccountVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="recAccountNo != null  and recAccountNo != ''"> and rec_account_no = #{recAccountNo}</if>
            <if test="recAccountName != null  and recAccountName != ''"> and rec_account_name like concat('%', #{recAccountName}, '%')</if>
            <if test="recAccountNumber != null  and recAccountNumber != ''"> and rec_account_number = #{recAccountNumber}</if>
            <if test="recOpenBankName != null  and recOpenBankName != ''"> and rec_open_bank_name like concat('%', #{recOpenBankName}, '%')</if>
            <if test="recOpenBankCode != null  and recOpenBankCode != ''"> and rec_open_bank_code = #{recOpenBankCode}</if>
        </where>
    </select>

    <select id="selectYqzlReceivingAccountById" parameterType="Long" resultMap="YqzlReceivingAccountResult">
        <include refid="selectYqzlReceivingAccountVo"/>
        where id = #{id}
    </select>

    <insert id="insertYqzlReceivingAccount" parameterType="YqzlReceivingAccount" useGeneratedKeys="true" keyProperty="id">
        insert into yqzl_receiving_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="recAccountNo != null">rec_account_no,</if>
            <if test="recAccountName != null">rec_account_name,</if>
            <if test="recAccountNumber != null">rec_account_number,</if>
            <if test="recOpenBankName != null">rec_open_bank_name,</if>
            <if test="recOpenBankCode != null">rec_open_bank_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="recAccountNo != null">#{recAccountNo},</if>
            <if test="recAccountName != null">#{recAccountName},</if>
            <if test="recAccountNumber != null">#{recAccountNumber},</if>
            <if test="recOpenBankName != null">#{recOpenBankName},</if>
            <if test="recOpenBankCode != null">#{recOpenBankCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateYqzlReceivingAccount" parameterType="YqzlReceivingAccount">
        update yqzl_receiving_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="recAccountNo != null">rec_account_no = #{recAccountNo},</if>
            <if test="recAccountName != null">rec_account_name = #{recAccountName},</if>
            <if test="recAccountNumber != null">rec_account_number = #{recAccountNumber},</if>
            <if test="recOpenBankName != null">rec_open_bank_name = #{recOpenBankName},</if>
            <if test="recOpenBankCode != null">rec_open_bank_code = #{recOpenBankCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYqzlReceivingAccountById" parameterType="Long">
        delete from yqzl_receiving_account where id = #{id}
    </delete>

    <delete id="deleteYqzlReceivingAccountByIds" parameterType="String">
        delete from yqzl_receiving_account where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
