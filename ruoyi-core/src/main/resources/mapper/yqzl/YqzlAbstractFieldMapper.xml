<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.yqzl.mapper.YqzlAbstractFieldMapper">

    <resultMap type="YqzlAbstractField" id="YqzlAbstractFieldResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="abstractField"    column="abstract_field"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectYqzlAbstractFieldVo">
        select id, user_id, abstract_field, create_by, create_time, update_by, update_time from yqzl_abstract_field
    </sql>

    <select id="selectYqzlAbstractFieldList" parameterType="YqzlAbstractField" resultMap="YqzlAbstractFieldResult">
        <include refid="selectYqzlAbstractFieldVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="abstractField != null  and abstractField != ''"> and abstract_field = #{abstractField}</if>
        </where>
    </select>

    <select id="selectYqzlAbstractFieldById" parameterType="Long" resultMap="YqzlAbstractFieldResult">
        <include refid="selectYqzlAbstractFieldVo"/>
        where id = #{id}
    </select>

    <insert id="insertYqzlAbstractField" parameterType="YqzlAbstractField" useGeneratedKeys="true" keyProperty="id">
        insert into yqzl_abstract_field
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="abstractField != null">abstract_field,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="abstractField != null">#{abstractField},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateYqzlAbstractField" parameterType="YqzlAbstractField">
        update yqzl_abstract_field
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="abstractField != null">abstract_field = #{abstractField},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYqzlAbstractFieldById" parameterType="Long">
        delete from yqzl_abstract_field where id = #{id}
    </delete>

    <delete id="deleteYqzlAbstractFieldByIds" parameterType="String">
        delete from yqzl_abstract_field where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
