package org.ruoyi.core.kaoqin.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.kaoqin.domain.DayLogGroup;
import org.ruoyi.core.kaoqin.mapper.DayLogGroupMapper;
import org.ruoyi.core.kaoqin.service.IDayLogGroupService;
import org.ruoyi.core.kaoqin.service.IDayLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 日志查询-快速分组列Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class DayLogGroupServiceImpl implements IDayLogGroupService
{
    @Autowired
    private DayLogGroupMapper dayLogGroupMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IDayLogService dayLogService;
    /**
     * 查询日志查询-快速分组列
     *
     * @param id 日志查询-快速分组列主键
     * @return 日志查询-快速分组列
     */
    @Override
    public DayLogGroup selectDayLogGroupById(Long id)
    {
        DayLogGroup dayLogGroup = dayLogGroupMapper.selectDayLogGroupById(id);

        if (dayLogGroup.getUserIdList() != null && !dayLogGroup.getUserIdList().isEmpty()){
            SysUser sysUser = new SysUser();
            sysUser.setUserIds(dayLogGroup.getUserIdList());
            List<SysUser> sysUsers = dayLogService.selectUserList(sysUser);
            dayLogGroup.setUserList(sysUsers);
        }
        return dayLogGroup;
    }

    public List<SysUser> selectGroupUserList(DayLogGroup group){
        SysUser user = new SysUser();
        user.setNickName(group.getNickName());
        user.setStatus("0");
        if (group.getId() != null){
            DayLogGroup dayLogGroup = selectDayLogGroupById(group.getId());
            user.setUserIds(dayLogGroup.getUserIdList());
            List<SysUser> sysUsers = sysUserService.selectUserList(user);
            List<String> usernameList = sysUsers.stream().map(SysUser::getUserName).collect(Collectors.toList());
            user.setUserNames(usernameList);
        }
        return  dayLogService.selectUserList(user);
    }

    /**
     * 查询日志查询-快速分组列列表
     *
     * @param dayLogGroup 日志查询-快速分组列
     * @return 日志查询-快速分组列
     */
    @Override
    public List<DayLogGroup> selectDayLogGroupList(DayLogGroup dayLogGroup)
    {
        dayLogGroup.setCreateBy(getUsername());
        List<DayLogGroup> dayLogGroups = dayLogGroupMapper.selectDayLogGroupList(dayLogGroup);

        List<Long> allUserIds = dayLogGroups.stream()
                    .filter(vo -> dayLogGroup.getUserIdList() != null && !dayLogGroup.getUserIdList().isEmpty())
                    .flatMap(vo -> dayLogGroup.getUserIdList().stream())
                    .collect(Collectors.toList());
        SysUser sysUser = new SysUser();
        sysUser.setUserIds(allUserIds);

        List<SysUser> sysUsers = sysUserService.selectUserList(sysUser);
        Map<Long, SysUser> userMap = sysUsers.stream()
                .collect(Collectors.toMap(
                        SysUser::getUserId,  // 使用 userId 作为键
                        user -> user,         // 使用 SysUser 对象本身作为值
                        (existing, replacement) -> existing // 在重复时保留第一个值
                ));

        dayLogGroups.forEach(vo -> {
            List<SysUser> users = vo.getUserIdList().stream()
                    .map(userMap::get) // 从 userMap 获取 nickName
                    .collect(Collectors.toList());
            vo.setUserList(users);
        });
        return dayLogGroups;
    }

    /**
     * 新增日志查询-快速分组列
     *
     * @param dayLogGroup 日志查询-快速分组列
     * @return 结果
     */
    @Override
    public int insertDayLogGroup(DayLogGroup dayLogGroup)
    {
        dayLogGroup.setCreateBy(getUsername());
        List<DayLogGroup> dayLogGroups = dayLogGroupMapper.getDayLogGroupList(dayLogGroup);
        if (dayLogGroups != null && !dayLogGroups.isEmpty()){
            throw new ServiceException("分组名称不可重复");
        }
        dayLogGroup.setCreateTime(DateUtils.getNowDate());
        return dayLogGroupMapper.insertDayLogGroup(dayLogGroup);
    }

    /**
     * 修改日志查询-快速分组列
     *
     * @param dayLogGroup 日志查询-快速分组列
     * @return 结果
     */
    @Override
    public int updateDayLogGroup(DayLogGroup dayLogGroup)
    {
        dayLogGroup.setUpdateBy(getUsername());
        List<DayLogGroup> dayLogGroups = dayLogGroupMapper.getDayLogGroupList(dayLogGroup).stream()
                .filter(group -> !dayLogGroup.getId().equals(group.getId()))
                .collect(Collectors.toList());  // 收集为一个新的列表
        if (!dayLogGroups.isEmpty()){
            throw new ServiceException("分组名称不可重复");
        }
        dayLogGroup.setUpdateTime(DateUtils.getNowDate());
        return dayLogGroupMapper.updateDayLogGroup(dayLogGroup);
    }

    /**
     * 批量删除日志查询-快速分组列
     *
     * @param ids 需要删除的日志查询-快速分组列主键
     * @return 结果
     */
    @Override
    public int deleteDayLogGroupByIds(Long[] ids)
    {
        return dayLogGroupMapper.deleteDayLogGroupByIds(ids);
    }

    /**
     * 删除日志查询-快速分组列信息
     *
     * @param id 日志查询-快速分组列主键
     * @return 结果
     */
    @Override
    public int deleteDayLogGroupById(Long id)
    {
        return dayLogGroupMapper.deleteDayLogGroupById(id);
    }
}
