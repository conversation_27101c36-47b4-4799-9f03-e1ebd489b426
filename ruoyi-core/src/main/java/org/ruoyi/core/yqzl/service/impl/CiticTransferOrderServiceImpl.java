package org.ruoyi.core.yqzl.service.impl;

import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.dom4j.DocumentException;
import org.ruoyi.core.yqzl.domain.CiticTransferOrder;
import org.ruoyi.core.yqzl.domain.YqzlAbstractField;
import org.ruoyi.core.yqzl.domain.YqzlNotify;
import org.ruoyi.core.yqzl.domain.YqzlReceivingAccount;
import org.ruoyi.core.yqzl.domain.rep.TransferOrderResponse;
import org.ruoyi.core.yqzl.domain.rep.TransferOrderResponseRow;
import org.ruoyi.core.yqzl.domain.util.CiticXml;
import org.ruoyi.core.yqzl.domain.util.XmlMessageParser;
import org.ruoyi.core.yqzl.domain.vo.CiticTransferOrderVo;
import org.ruoyi.core.yqzl.mapper.CiticTransferOrderMapper;
import org.ruoyi.core.yqzl.service.ICiticTransferOrderService;
import org.ruoyi.core.yqzl.service.IYqzlAbstractFieldService;
import org.ruoyi.core.yqzl.service.IYqzlNotifyService;
import org.ruoyi.core.yqzl.service.IYqzlReceivingAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 中信银行支付转账Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class CiticTransferOrderServiceImpl implements ICiticTransferOrderService
{
    @Autowired
    private CiticTransferOrderMapper citicTransferOrderMapper;
    @Autowired
    private CiticXml citicXml;
    @Autowired
    private IYqzlNotifyService yqzlNotifyService;
    @Autowired
    private IYqzlAbstractFieldService yqzlAbstractFieldService;
    @Autowired
    private IYqzlReceivingAccountService yqzlReceivingAccountService;
    /**
     * 查询中信银行支付转账
     *
     * @param id 中信银行支付转账主键
     * @return 中信银行支付转账
     */
    @Override
    public CiticTransferOrderVo selectCiticTransferOrderById(Long id)
    {
        return citicTransferOrderMapper.selectCiticTransferOrderById(id);
    }

    /**
     * 查询中信银行支付转账列表
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 中信银行支付转账
     */
    @Override
    public List<CiticTransferOrderVo> selectCiticTransferOrderList(CiticTransferOrderVo citicTransferOrder)
    {
        return citicTransferOrderMapper.selectCiticTransferOrderList(citicTransferOrder);
    }

    /**
     * 新增中信银行支付转账
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 结果
     */
    @Override
    public int insertCiticTransferOrder(CiticTransferOrder citicTransferOrder) {
        citicTransferOrder.setClientID(String.valueOf(System.currentTimeMillis()));

        int i = citicTransferOrderMapper.insertCiticTransferOrder(citicTransferOrder);
        if ("2".equals(citicTransferOrder.getState())){
            YqzlNotify yqzlNotify = new YqzlNotify();
            yqzlNotify.setNotifyModule("您有转账审核，请尽快处理！");
            yqzlNotify.setUrl("");
            yqzlNotify.setNotifyType("1");
            yqzlNotify.setNotifyMsg("您有转账审核，请尽快处理！!!!!");
            yqzlNotify.setDisposeUser(getLoginUser().getUserId());
            yqzlNotify.setRemindText("账号[" + citicTransferOrder.getPayAccountNo() + "]发起转账申请，请您尽快审核！");
            yqzlNotify.setCorrelationId(citicTransferOrder.getId());
            yqzlNotifyService.insertYqzlNotify(yqzlNotify);
        }
        return i;
    }

    /**
     * 修改中信银行支付转账
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 结果
     */
    @Override
    public int updateCiticTransferOrder(CiticTransferOrder citicTransferOrder)
    {
        if ("2".equals(citicTransferOrder.getState())){
            YqzlNotify yqzlNotify = new YqzlNotify();
            yqzlNotify.setNotifyModule("转账审核提醒");
            yqzlNotify.setUrl("");
            yqzlNotify.setNotifyType("1");
            yqzlNotify.setNotifyMsg("您有转账审核，请尽快处理！!!!!");
            yqzlNotify.setDisposeUser(getLoginUser().getUserId());
            yqzlNotify.setRemindText("账号[" + citicTransferOrder.getPayAccountNo() + "]发起转账申请，请您尽快审核！");
            yqzlNotify.setCorrelationId(citicTransferOrder.getId());
            yqzlNotifyService.insertYqzlNotify(yqzlNotify);
        }
        return citicTransferOrderMapper.updateCiticTransferOrder(citicTransferOrder);
    }

    /**
     * 批量删除中信银行支付转账
     *
     * @param ids 需要删除的中信银行支付转账主键
     * @return 结果
     */
    @Override
    public int deleteCiticTransferOrderByIds(Long[] ids)
    {
        return citicTransferOrderMapper.deleteCiticTransferOrderByIds(ids);
    }

    /**
     * 删除中信银行支付转账信息
     *
     * @param id 中信银行支付转账主键
     * @return 结果
     */
    @Override
    public int deleteCiticTransferOrderById(Long id)
    {
        return citicTransferOrderMapper.deleteCiticTransferOrderById(id);
    }

    @Override
    public int passTransferOrderById(CiticTransferOrder citicTransferOrder) throws Exception {
        // 数据处理逻辑：处理预约支付相关字段
        processPrePaymentData(citicTransferOrder);

        initiateTransfer(citicTransferOrder);
        citicTransferOrder.setState("3");
        citicTransferOrder.setReviewTime(new Date());
        //新增附言
        if(citicTransferOrder.getAbstractField() != null && !citicTransferOrder.getAbstractField().isEmpty()){
            YqzlAbstractField yqzlAbstractField = new YqzlAbstractField();
            yqzlAbstractField.setUserId(getLoginUser().getUserId());
            yqzlAbstractField.setAbstractField(citicTransferOrder.getAbstractField());
            if (yqzlAbstractFieldService.selectYqzlAbstractFieldList(yqzlAbstractField).isEmpty()){
                yqzlAbstractFieldService.insertYqzlAbstractField(yqzlAbstractField);
            }
        }
        //新增收款账号
        if (citicTransferOrder.getRecAccountNo() != null && !citicTransferOrder.getRecAccountNo().isEmpty()){
            YqzlReceivingAccount yqzlReceivingAccount = new YqzlReceivingAccount();
            yqzlReceivingAccount.setUserId(getLoginUser().getUserId());
            yqzlReceivingAccount.setRecAccountNo(citicTransferOrder.getRecAccountNo());
            if (yqzlReceivingAccountService.selectYqzlReceivingAccountList(yqzlReceivingAccount).isEmpty()){
                yqzlReceivingAccount.setRecAccountName(citicTransferOrder.getRecAccountNo());
                yqzlReceivingAccount.setRecOpenBankName(citicTransferOrder.getRecOpenBankName());
                yqzlReceivingAccount.setRecOpenBankCode(citicTransferOrder.getRecOpenBankCode());
                yqzlReceivingAccount.setRecAccountNumber(citicTransferOrder.getRecAccountNumber());
                yqzlReceivingAccountService.insertYqzlReceivingAccount(yqzlReceivingAccount);
            }
        }

        return citicTransferOrderMapper.updateCiticTransferOrder(citicTransferOrder);
    }

    /**
     * 处理预约支付数据
     * @param citicTransferOrder 转账订单
     */
    private void processPrePaymentData(CiticTransferOrder citicTransferOrder) {
        String preFlg = citicTransferOrder.getPreFlg();
        Date currentTime = new Date();

        if ("1".equals(preFlg)) {
            // preFlg=1(按时间预约) 处理逻辑
            String preDate = citicTransferOrder.getPreDate();
            String preTime = citicTransferOrder.getPreTime();

            if (preDate != null && !preDate.trim().isEmpty() &&
                preTime != null && !preTime.trim().isEmpty()) {
                try {
                    // 将preDate(YYYY-MM-DD)和preTime(hh:mm:ss)组合成完整时间
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String combinedDateTime = preDate + " " + preTime;
                    Date scheduledTime = dateFormat.parse(combinedDateTime);

                    // 如果预约时间超过当前时间，则清空预约信息
                    if (scheduledTime.before(currentTime)) {
                        citicTransferOrder.setPreDate("");
                        citicTransferOrder.setPreTime("");
                        citicTransferOrder.setPreFlg("0");
                    } else {
                        //重置回显格式
                        citicTransferOrder.setPreDate(preDate.trim().replace("-", ""));
                        citicTransferOrder.setPreTime(preTime.trim().replace(":", ""));
                    }
                } catch (Exception e) {
                    // 时间解析失败时，清空预约信息
                    citicTransferOrder.setPreDate("");
                    citicTransferOrder.setPreTime("");
                    citicTransferOrder.setPreFlg("0");
                }
            }
        } else if ("2".equals(preFlg)) {
            // preFlg=2(次日) 处理逻辑
            Date createTime = citicTransferOrder.getCreateTime();
            if (createTime != null) {
                // 计算createTime + 1天
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(createTime);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                Date nextDay = calendar.getTime();

                // 如果当前时间超过createTime + 1天，则将preFlg设为0
                if (currentTime.after(nextDay)) {
                    citicTransferOrder.setPreFlg("0");
                }
            }
        }
    }

    @Override
    public int unpassTransferOrderById(CiticTransferOrder citicTransferOrder){
        citicTransferOrder.setState("8");
        citicTransferOrder.setReviewTime(new Date());
        return citicTransferOrderMapper.updateCiticTransferOrder(citicTransferOrder);
    }

    @Override
    public void initiateTransfer(CiticTransferOrder citicTransferOrder) throws Exception {
        List<CiticTransferOrder> citicTransferOrders = new ArrayList<>();
        citicTransferOrders.add(citicTransferOrder);
        String responseXMl = citicXml.responseXMlList("DLINTTRN", citicTransferOrders);
        TransferOrderResponse transferOrderResponse = XmlMessageParser.parseXmlToEntity(responseXMl, TransferOrderResponse.class);
        if ("AAAAAAA".equals(transferOrderResponse.getStatus())) {
            TransferOrderResponseRow first = transferOrderResponse.getRow().stream().findFirst().orElse(null);
            assert first != null;
            if (!"AAAAAAE".equals(first.getStatus())) {
                citicTransferOrder.setState("7");
                //根据报错更新报错信息方便后续查原因
                citicTransferOrder.setStatusText(first.getStatusText());
                citicTransferOrderMapper.updateCiticTransferOrder(citicTransferOrder);
                throw new RuntimeException(first.getStatusText());
            }
        } else {
            citicTransferOrder.setStatusText(transferOrderResponse.getStatusText());
            //根据报错更新报错信息方便后续查原因
            citicTransferOrder.setState("7");
            citicTransferOrderMapper.updateCiticTransferOrder(citicTransferOrder);
            throw new RuntimeException(transferOrderResponse.getStatusText());
        }
    }
}
