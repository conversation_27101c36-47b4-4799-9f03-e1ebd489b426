package org.ruoyi.core.yqzl.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.service.INewAuthorityService;
import org.ruoyi.core.oasystem.domain.OaTrader;
import org.ruoyi.core.oasystem.service.IOaTraderService;
import org.ruoyi.core.yqzl.domain.CiticAccountBalanceRow;
import org.ruoyi.core.yqzl.domain.req.CiticAccountBalanceQuery;
import org.ruoyi.core.yqzl.domain.util.CiticXml;
import org.ruoyi.core.yqzl.domain.util.XmlMessageGenerator;
import org.ruoyi.core.yqzl.domain.util.XmlMessageParser;
import org.ruoyi.core.yqzl.domain.vo.AccountManagerVo;
import org.ruoyi.core.yqzl.domain.vo.CiticAccountBalanceRowVo;
import org.ruoyi.core.yqzl.mapper.CiticAccountBalanceRowMapper;
import org.ruoyi.core.yqzl.service.IAccountManagerService;
import org.ruoyi.core.yqzl.service.ICiticAccountBalanceRowService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import static com.ruoyi.common.utils.SecurityUtils.getUserId;

/**
 * 中信银行账户余额查询结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class CiticAccountBalanceRowServiceImpl implements ICiticAccountBalanceRowService
{
    private static final Logger logger = LoggerFactory.getLogger(CiticAccountBalanceRowServiceImpl.class);

    @Autowired
    private CiticAccountBalanceRowMapper citicAccountBalanceRowMapper;
    @Autowired
    private CiticXml citicXml;
    @Autowired
    private INewAuthorityService newAuthorityService;
    @Autowired
    private IOaTraderService oaTraderService;
    @Autowired
    private IAccountManagerService accountManagerService;
    /**
     * 查询中信银行账户余额查询结果
     *
     * @param id 中信银行账户余额查询结果主键
     * @return 中信银行账户余额查询结果
     */
    @Override
    public CiticAccountBalanceRow selectCiticAccountBalanceRowById(Long id)
    {
        return citicAccountBalanceRowMapper.selectCiticAccountBalanceRowById(id);
    }

    /**
     * 查询中信银行账户余额查询结果列表
     *
     * @param citicAccountBalanceRow 中信银行账户余额查询结果
     * @return 中信银行账户余额查询结果
     */
    @Override
    public List<CiticAccountBalanceRow> selectCiticAccountBalanceRowList(CiticAccountBalanceRowVo citicAccountBalanceRow)
    {

        List<Long> comapnyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(getUserId(), AuthModuleEnum.DEBTCONVERSION.getCode());
        OaTrader oaTrader = new OaTrader();
        oaTrader.setBankOfDeposit("中信银行");
        oaTrader.setCompanyIdList(comapnyIds);
        return citicAccountBalanceRowMapper.selectCiticAccountBalanceRowList(citicAccountBalanceRow);
    }

    /**
     * 新增中信银行账户余额查询结果
     *
     * @param citicAccountBalanceRow 中信银行账户余额查询结果
     * @return 结果
     */
    @Override
    public int insertCiticAccountBalanceRow(CiticAccountBalanceRow citicAccountBalanceRow)
    {
        return citicAccountBalanceRowMapper.insertCiticAccountBalanceRow(citicAccountBalanceRow);
    }

    /**
     * 修改中信银行账户余额查询结果
     *
     * @param citicAccountBalanceRow 中信银行账户余额查询结果
     * @return 结果
     */
    @Override
    public int updateCiticAccountBalanceRow(CiticAccountBalanceRow citicAccountBalanceRow)
    {
        return citicAccountBalanceRowMapper.updateCiticAccountBalanceRow(citicAccountBalanceRow);
    }

    /**
     * 批量删除中信银行账户余额查询结果
     *
     * @param ids 需要删除的中信银行账户余额查询结果主键
     * @return 结果
     */
    @Override
    public int deleteCiticAccountBalanceRowByIds(Long[] ids)
    {
        return citicAccountBalanceRowMapper.deleteCiticAccountBalanceRowByIds(ids);
    }

    /**
     * 删除中信银行账户余额查询结果信息
     *
     * @param id 中信银行账户余额查询结果主键
     * @return 结果
     */
    @Override
    public int deleteCiticAccountBalanceRowById(Long id)
    {
        return citicAccountBalanceRowMapper.deleteCiticAccountBalanceRowById(id);
    }
    /**
     * 批量新增中信银行账户余额查询结果
     *
     * @param list 中信银行账户余额查询结果
     * @return 结果
     */
    public int batchInsertCiticAccountBalanceRow(List<CiticAccountBalanceRow> list) {
        return citicAccountBalanceRowMapper.batchInsertCiticAccountBalanceRow(list);
    }

    @Override
    public int GenerationDailyAccountBalanceRow() throws Exception {
        AccountManagerVo accountManagerVo = new AccountManagerVo();
        accountManagerVo.setOpenBank("中信银行");
        List<AccountManagerVo> accountManagerVos = accountManagerService.selectAccountManagerList(accountManagerVo);
        if (accountManagerVos.isEmpty()) {
            logger.info( "中信银行-账户余额查询结果对象:未查询到中信银行账号");
        }
        //查询中信银行号
        List<CiticAccountBalanceQuery> query = new ArrayList<>();
        accountManagerVos.forEach(trader -> {
            CiticAccountBalanceQuery balanceQuery = new CiticAccountBalanceQuery();
            balanceQuery.setAccountNo(trader.getBankAccount());
            query.add(balanceQuery);
        });

        String responseXMl = citicXml.responseXMlList("DLBALQRY", query);
        logger.info("中信银行-账户余额查询结果对象-返回报文:" + responseXMl);
        List<CiticAccountBalanceRow> resultList = XmlMessageParser.parseXmlToEntityList(responseXMl,CiticAccountBalanceRow.class);

        // 过滤只保留status为AAAAAAA的对象
        resultList = resultList.stream()
                .filter(item -> "AAAAAAA".equals(item.getStatus()))
                .collect(java.util.stream.Collectors.toList());
        if (resultList.isEmpty()) {
            logger.info("中信银行-账户余额查询结果对象:查询过滤后只保留status为AAAAAAA的对象,无数据");
        }

        CiticAccountBalanceRowVo citicAccountBalanceRowVo = new CiticAccountBalanceRowVo();
        citicAccountBalanceRowVo.setQueryDate(new Date());
        List<CiticAccountBalanceRow> citicAccountBalanceRows = citicAccountBalanceRowMapper.selectCiticAccountBalanceRowList(citicAccountBalanceRowVo);

        return citicAccountBalanceRowMapper.batchInsertCiticAccountBalanceRow(resultList);
    }

    /**
     * 查询账户余额
     * @param citicAccountBalanceRow
     * @return
     * @throws Exception
     */
    @Override
    public CiticAccountBalanceRow getCiticAccountBalanceRow(CiticAccountBalanceRowVo citicAccountBalanceRow) throws Exception {
        List<CiticAccountBalanceQuery> query = new ArrayList<>();
        CiticAccountBalanceQuery balanceQuery = new CiticAccountBalanceQuery();
        balanceQuery.setAccountNo(citicAccountBalanceRow.getAccountNo());
        query.add(balanceQuery);
        String responseXMl = citicXml.responseXMlList("DLBALQRY", query);

        List<CiticAccountBalanceRow> resultList = XmlMessageParser.parseXmlToEntityList(responseXMl,CiticAccountBalanceRow.class);
        if (!resultList.isEmpty()) {
            return resultList.get(0);
        } else {
            // 处理空列表的情况
            throw new Exception("未查询账户余额");
        }
    }
}
