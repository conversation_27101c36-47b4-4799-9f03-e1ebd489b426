package org.ruoyi.core.yqzl.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 银企收款账号对象 yqzl_receiving_account
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
public class YqzlReceivingAccount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 账户 */
    @Excel(name = "账户")
    private String recAccountNo;

    /** 账户名称 */
    @Excel(name = "账户名称")
    private String recAccountName;

    /** 账户编号 */
    @Excel(name = "账户编号")
    private String recAccountNumber;

    /** 收款开户行名 */
    @Excel(name = "收款开户行名")
    private String recOpenBankName;

    /** 收款开户行网点号 */
    @Excel(name = "收款开户行网点号")
    private String recOpenBankCode;

}
