package org.ruoyi.core.yqzl.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.yqzl.domain.YqzlAbstractField;
import org.ruoyi.core.yqzl.mapper.YqzlAbstractFieldMapper;
import org.ruoyi.core.yqzl.service.IYqzlAbstractFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 银企直联附言Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class YqzlAbstractFieldServiceImpl implements IYqzlAbstractFieldService
{
    @Autowired
    private YqzlAbstractFieldMapper yqzlAbstractFieldMapper;

    /**
     * 查询银企直联附言
     *
     * @param id 银企直联附言主键
     * @return 银企直联附言
     */
    @Override
    public YqzlAbstractField selectYqzlAbstractFieldById(Long id)
    {
        return yqzlAbstractFieldMapper.selectYqzlAbstractFieldById(id);
    }

    /**
     * 查询银企直联附言列表
     *
     * @param yqzlAbstractField 银企直联附言
     * @return 银企直联附言
     */
    @Override
    public List<YqzlAbstractField> selectYqzlAbstractFieldList(YqzlAbstractField yqzlAbstractField)
    {
        return yqzlAbstractFieldMapper.selectYqzlAbstractFieldList(yqzlAbstractField);
    }

    /**
     * 新增银企直联附言
     *
     * @param yqzlAbstractField 银企直联附言
     * @return 结果
     */
    @Override
    public int insertYqzlAbstractField(YqzlAbstractField yqzlAbstractField)
    {
        yqzlAbstractField.setCreateTime(DateUtils.getNowDate());
        return yqzlAbstractFieldMapper.insertYqzlAbstractField(yqzlAbstractField);
    }

    /**
     * 修改银企直联附言
     *
     * @param yqzlAbstractField 银企直联附言
     * @return 结果
     */
    @Override
    public int updateYqzlAbstractField(YqzlAbstractField yqzlAbstractField)
    {
        yqzlAbstractField.setUpdateTime(DateUtils.getNowDate());
        return yqzlAbstractFieldMapper.updateYqzlAbstractField(yqzlAbstractField);
    }

    /**
     * 批量删除银企直联附言
     *
     * @param ids 需要删除的银企直联附言主键
     * @return 结果
     */
    @Override
    public int deleteYqzlAbstractFieldByIds(Long[] ids)
    {
        return yqzlAbstractFieldMapper.deleteYqzlAbstractFieldByIds(ids);
    }

    /**
     * 删除银企直联附言信息
     *
     * @param id 银企直联附言主键
     * @return 结果
     */
    @Override
    public int deleteYqzlAbstractFieldById(Long id)
    {
        return yqzlAbstractFieldMapper.deleteYqzlAbstractFieldById(id);
    }
}
