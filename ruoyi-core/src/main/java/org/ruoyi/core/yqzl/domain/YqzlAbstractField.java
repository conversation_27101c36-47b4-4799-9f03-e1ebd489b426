package org.ruoyi.core.yqzl.domain;

import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 银企直联附言对象 yqzl_abstract_field
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
public class YqzlAbstractField extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 附言 */
    @Excel(name = "附言")
    private String abstractField;

}
