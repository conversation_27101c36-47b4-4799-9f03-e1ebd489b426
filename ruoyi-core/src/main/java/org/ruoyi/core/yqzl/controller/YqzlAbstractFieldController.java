package org.ruoyi.core.yqzl.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.yqzl.domain.YqzlAbstractField;
import org.ruoyi.core.yqzl.service.IYqzlAbstractFieldService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 银企直联附言Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/yqzl/abstract/field")
public class YqzlAbstractFieldController extends BaseController
{
    @Autowired
    private IYqzlAbstractFieldService yqzlAbstractFieldService;

    /**
     * 查询银企直联附言列表
     */
    //@PreAuthorize("@ss.hasPermi('system:field:list')")
    @GetMapping("/list")
    public TableDataInfo list(YqzlAbstractField yqzlAbstractField)
    {
        startPage();
        List<YqzlAbstractField> list = yqzlAbstractFieldService.selectYqzlAbstractFieldList(yqzlAbstractField);
        return getDataTable(list);
    }

    /**
     * 导出银企直联附言列表
     */
    //@PreAuthorize("@ss.hasPermi('system:field:export')")
    @Log(title = "银企直联附言", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YqzlAbstractField yqzlAbstractField)
    {
        List<YqzlAbstractField> list = yqzlAbstractFieldService.selectYqzlAbstractFieldList(yqzlAbstractField);
        ExcelUtil<YqzlAbstractField> util = new ExcelUtil<YqzlAbstractField>(YqzlAbstractField.class);
        util.exportExcel(response, list, "银企直联附言数据");
    }

    /**
     * 获取银企直联附言详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:field:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(yqzlAbstractFieldService.selectYqzlAbstractFieldById(id));
    }

    /**
     * 新增银企直联附言
     */
    //@PreAuthorize("@ss.hasPermi('system:field:add')")
    @Log(title = "银企直联附言", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YqzlAbstractField yqzlAbstractField)
    {
        return toAjax(yqzlAbstractFieldService.insertYqzlAbstractField(yqzlAbstractField));
    }

    /**
     * 修改银企直联附言
     */
    //@PreAuthorize("@ss.hasPermi('system:field:edit')")
    @Log(title = "银企直联附言", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YqzlAbstractField yqzlAbstractField)
    {
        return toAjax(yqzlAbstractFieldService.updateYqzlAbstractField(yqzlAbstractField));
    }

    /**
     * 删除银企直联附言
     */
    //@PreAuthorize("@ss.hasPermi('system:field:remove')")
    @Log(title = "银企直联附言", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(yqzlAbstractFieldService.deleteYqzlAbstractFieldByIds(ids));
    }
}
