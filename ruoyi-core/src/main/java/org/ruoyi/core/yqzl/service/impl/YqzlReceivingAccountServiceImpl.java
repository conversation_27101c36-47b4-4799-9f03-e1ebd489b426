package org.ruoyi.core.yqzl.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.yqzl.domain.YqzlReceivingAccount;
import org.ruoyi.core.yqzl.mapper.YqzlReceivingAccountMapper;
import org.ruoyi.core.yqzl.service.IYqzlReceivingAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 银企收款账号Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class YqzlReceivingAccountServiceImpl implements IYqzlReceivingAccountService
{
    @Autowired
    private YqzlReceivingAccountMapper yqzlReceivingAccountMapper;

    /**
     * 查询银企收款账号
     *
     * @param id 银企收款账号主键
     * @return 银企收款账号
     */
    @Override
    public YqzlReceivingAccount selectYqzlReceivingAccountById(Long id)
    {
        return yqzlReceivingAccountMapper.selectYqzlReceivingAccountById(id);
    }

    /**
     * 查询银企收款账号列表
     *
     * @param yqzlReceivingAccount 银企收款账号
     * @return 银企收款账号
     */
    @Override
    public List<YqzlReceivingAccount> selectYqzlReceivingAccountList(YqzlReceivingAccount yqzlReceivingAccount)
    {
        return yqzlReceivingAccountMapper.selectYqzlReceivingAccountList(yqzlReceivingAccount);
    }

    /**
     * 新增银企收款账号
     *
     * @param yqzlReceivingAccount 银企收款账号
     * @return 结果
     */
    @Override
    public int insertYqzlReceivingAccount(YqzlReceivingAccount yqzlReceivingAccount)
    {
        yqzlReceivingAccount.setCreateBy(getUsername());
        yqzlReceivingAccount.setCreateTime(DateUtils.getNowDate());
        return yqzlReceivingAccountMapper.insertYqzlReceivingAccount(yqzlReceivingAccount);
    }

    /**
     * 修改银企收款账号
     *
     * @param yqzlReceivingAccount 银企收款账号
     * @return 结果
     */
    @Override
    public int updateYqzlReceivingAccount(YqzlReceivingAccount yqzlReceivingAccount)
    {
        yqzlReceivingAccount.setUpdateTime(DateUtils.getNowDate());
        return yqzlReceivingAccountMapper.updateYqzlReceivingAccount(yqzlReceivingAccount);
    }

    /**
     * 批量删除银企收款账号
     *
     * @param ids 需要删除的银企收款账号主键
     * @return 结果
     */
    @Override
    public int deleteYqzlReceivingAccountByIds(Long[] ids)
    {
        return yqzlReceivingAccountMapper.deleteYqzlReceivingAccountByIds(ids);
    }

    /**
     * 删除银企收款账号信息
     *
     * @param id 银企收款账号主键
     * @return 结果
     */
    @Override
    public int deleteYqzlReceivingAccountById(Long id)
    {
        return yqzlReceivingAccountMapper.deleteYqzlReceivingAccountById(id);
    }
}
