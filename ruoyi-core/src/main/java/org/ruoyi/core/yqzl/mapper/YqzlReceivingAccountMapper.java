package org.ruoyi.core.yqzl.mapper;

import org.ruoyi.core.yqzl.domain.YqzlReceivingAccount;

import java.util.List;

/**
 * 银企收款账号Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface YqzlReceivingAccountMapper
{
    /**
     * 查询银企收款账号
     *
     * @param id 银企收款账号主键
     * @return 银企收款账号
     */
    public YqzlReceivingAccount selectYqzlReceivingAccountById(Long id);

    /**
     * 查询银企收款账号列表
     *
     * @param yqzlReceivingAccount 银企收款账号
     * @return 银企收款账号集合
     */
    public List<YqzlReceivingAccount> selectYqzlReceivingAccountList(YqzlReceivingAccount yqzlReceivingAccount);

    /**
     * 新增银企收款账号
     *
     * @param yqzlReceivingAccount 银企收款账号
     * @return 结果
     */
    public int insertYqzlReceivingAccount(YqzlReceivingAccount yqzlReceivingAccount);

    /**
     * 修改银企收款账号
     *
     * @param yqzlReceivingAccount 银企收款账号
     * @return 结果
     */
    public int updateYqzlReceivingAccount(YqzlReceivingAccount yqzlReceivingAccount);

    /**
     * 删除银企收款账号
     *
     * @param id 银企收款账号主键
     * @return 结果
     */
    public int deleteYqzlReceivingAccountById(Long id);

    /**
     * 批量删除银企收款账号
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYqzlReceivingAccountByIds(Long[] ids);
}
