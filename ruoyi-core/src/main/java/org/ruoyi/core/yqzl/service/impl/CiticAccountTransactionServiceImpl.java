package org.ruoyi.core.yqzl.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import org.ruoyi.core.yqzl.domain.CiticAccountTransaction;
import org.ruoyi.core.yqzl.domain.rep.CiticAccountTransactionResponse;
import org.ruoyi.core.yqzl.domain.req.CiticTransactionQuery;
import org.ruoyi.core.yqzl.domain.util.CiticXml;
import org.ruoyi.core.yqzl.domain.util.XmlMessageParser;
import org.ruoyi.core.yqzl.domain.vo.CiticAccountTransactionVo;
import org.ruoyi.core.yqzl.mapper.CiticAccountTransactionMapper;
import org.ruoyi.core.yqzl.service.ICiticAccountTransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 中信银行账户明细信息查询Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class CiticAccountTransactionServiceImpl implements ICiticAccountTransactionService
{
    @Autowired
    private CiticAccountTransactionMapper citicAccountTransactionMapper;
    @Autowired
    private CiticXml citicXml;

    private static final Logger logger = LoggerFactory.getLogger(CiticAccountTransactionServiceImpl.class);
    /**
     * 查询中信银行账户明细信息查询
     *
     * @param id 中信银行账户明细信息查询主键
     * @return 中信银行账户明细信息查询
     */
    @Override
    public CiticAccountTransaction selectCiticAccountTransactionById(Long id)
    {
        return citicAccountTransactionMapper.selectCiticAccountTransactionById(id);
    }

    /**
     * 查询中信银行账户明细信息查询列表
     *
     * @param citicAccountTransaction 中信银行账户明细信息查询
     * @return 中信银行账户明细信息查询
     */
    @Override
    public List<CiticAccountTransactionVo> selectCiticAccountTransactionList(CiticAccountTransactionVo citicAccountTransaction)
    {
        return citicAccountTransactionMapper.selectCiticAccountTransactionList(citicAccountTransaction);
    }

    /**
     * 新增中信银行账户明细信息查询
     *
     * @param citicAccountTransaction 中信银行账户明细信息查询
     * @return 结果
     */
    @Override
    public int insertCiticAccountTransaction(CiticAccountTransaction citicAccountTransaction)
    {
        return citicAccountTransactionMapper.insertCiticAccountTransaction(citicAccountTransaction);
    }

    /**
     * 修改中信银行账户明细信息查询
     *
     * @param citicAccountTransaction 中信银行账户明细信息查询
     * @return 结果
     */
    @Override
    public int updateCiticAccountTransaction(CiticAccountTransaction citicAccountTransaction)
    {
        return citicAccountTransactionMapper.updateCiticAccountTransaction(citicAccountTransaction);
    }

    /**
     * 批量删除中信银行账户明细信息查询
     *
     * @param ids 需要删除的中信银行账户明细信息查询主键
     * @return 结果
     */
    @Override
    public int deleteCiticAccountTransactionByIds(Long[] ids)
    {
        return citicAccountTransactionMapper.deleteCiticAccountTransactionByIds(ids);
    }

    /**
     * 删除中信银行账户明细信息查询信息
     *
     * @param id 中信银行账户明细信息查询主键
     * @return 结果
     */
    @Override
    public int deleteCiticAccountTransactionById(Long id)
    {
        return citicAccountTransactionMapper.deleteCiticAccountTransactionById(id);
    }

    /**
     * 批量新增中信银行账户明细信息查询
     *
     * @param list 中信银行账户明细信息查询
     * @return 结果
     */
    @Override
    public int batchInsertCiticAccountTransaction(List<CiticAccountTransaction> list) {
        return citicAccountTransactionMapper.batchInsertCiticAccountTransaction(list);
    }

    /**
     * 定时任务查询每日
     * @return
     * @throws Exception
     */
    @Override
    public int GenerationDailyAccountTransaction() throws Exception{
        CiticTransactionQuery query = new CiticTransactionQuery();
        //FIXME:查询条件暂时
        query.setAccountNo("8110701014101248723");
        query.setLowAmount(new BigDecimal(0));
        query.setUpAmount(new BigDecimal(*********));
        query.setStartDate("********");
        query.setEndDate("********");
        query.setControlFlag("2");
        query.setPageNumber(10); // 每页最多10条记录

        // 存储所有查询到的数据
        List<CiticAccountTransaction> totalTransactions = new ArrayList<>();
        int startRecord = 1;
        Integer totalCount = null;

        logger.info("[{}] 开始查询中信银行账户明细信息查询，账户号：{}",
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            query.getAccountNo());

        // 分页查询所有数据
        while (true) {
            query.setStartRecord(String.format("%04d", startRecord));

            String responseXMl = citicXml.responseXMl("DLTRNALL", query);

            CiticAccountTransactionResponse accountTransactionXmlParseResult = XmlMessageParser.parseXmlToEntity(responseXMl, CiticAccountTransactionResponse.class);
            // 检查响应状态
            if (!"AAAAAAA".equals(accountTransactionXmlParseResult.getStatus())) {
                String error = accountTransactionXmlParseResult.getStatus() + ":" + accountTransactionXmlParseResult.getStatusText();
                logger.error("[{}] 中信银行返回错误：{}",
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), error);
                throw new ServiceException("中信银行返回错误:" + error);
            }

            // 第一次查询时获取总记录数
            if (totalCount == null) {
                totalCount = accountTransactionXmlParseResult.getTotalRecords();
            }

            List<CiticAccountTransaction> pageData = accountTransactionXmlParseResult.getRow();

            // 中信接口返回数据为空时直接返回
            if (pageData == null || pageData.isEmpty()) {
                break;
            }

            // 设置账户信息
            pageData.forEach(citicAccountTransaction -> {
                citicAccountTransaction.setAccountNo(accountTransactionXmlParseResult.getAccountNo());
                citicAccountTransaction.setOpenBankName(accountTransactionXmlParseResult.getOpenBankName());
            });

            totalTransactions.addAll(pageData);


            // 如果当前页数据量小于页面大小，说明已经是最后一页
            if (pageData.size() < query.getPageNumber()) {
                break;
            }

            // 如果已经获取了所有数据，跳出循环
            if (totalCount != null && totalTransactions.size() >= totalCount) {
                break;
            }

            startRecord += pageData.size();
        }

        // 批量插入所有查询到的数据
        if (totalTransactions.isEmpty()) {
            logger.info("[{}] 未查询到任何数据",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            return 0;
        }

        return batchInsertCiticAccountTransaction(totalTransactions);
    }
}
