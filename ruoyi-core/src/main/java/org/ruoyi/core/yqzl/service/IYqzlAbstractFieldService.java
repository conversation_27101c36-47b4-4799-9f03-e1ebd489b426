package org.ruoyi.core.yqzl.service;

import org.ruoyi.core.yqzl.domain.YqzlAbstractField;

import java.util.List;

/**
 * 银企直联附言Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IYqzlAbstractFieldService
{
    /**
     * 查询银企直联附言
     *
     * @param id 银企直联附言主键
     * @return 银企直联附言
     */
    public YqzlAbstractField selectYqzlAbstractFieldById(Long id);

    /**
     * 查询银企直联附言列表
     *
     * @param yqzlAbstractField 银企直联附言
     * @return 银企直联附言集合
     */
    public List<YqzlAbstractField> selectYqzlAbstractFieldList(YqzlAbstractField yqzlAbstractField);

    /**
     * 新增银企直联附言
     *
     * @param yqzlAbstractField 银企直联附言
     * @return 结果
     */
    public int insertYqzlAbstractField(YqzlAbstractField yqzlAbstractField);

    /**
     * 修改银企直联附言
     *
     * @param yqzlAbstractField 银企直联附言
     * @return 结果
     */
    public int updateYqzlAbstractField(YqzlAbstractField yqzlAbstractField);

    /**
     * 批量删除银企直联附言
     *
     * @param ids 需要删除的银企直联附言主键集合
     * @return 结果
     */
    public int deleteYqzlAbstractFieldByIds(Long[] ids);

    /**
     * 删除银企直联附言信息
     *
     * @param id 银企直联附言主键
     * @return 结果
     */
    public int deleteYqzlAbstractFieldById(Long id);
}
