package org.ruoyi.core.yqzl.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.yqzl.domain.YqzlReceivingAccount;
import org.ruoyi.core.yqzl.service.IYqzlReceivingAccountService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 银企收款账号Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/yqzl/receiving/account")
public class YqzlReceivingAccountController extends BaseController
{
    @Autowired
    private IYqzlReceivingAccountService yqzlReceivingAccountService;

    /**
     * 查询银企收款账号列表
     */
    //@PreAuthorize("@ss.hasPermi('system:account:list')")
    @GetMapping("/list")
    public TableDataInfo list(YqzlReceivingAccount yqzlReceivingAccount)
    {
        startPage();
        List<YqzlReceivingAccount> list = yqzlReceivingAccountService.selectYqzlReceivingAccountList(yqzlReceivingAccount);
        return getDataTable(list);
    }

    /**
     * 导出银企收款账号列表
     */
    //@PreAuthorize("@ss.hasPermi('system:account:export')")
    @Log(title = "银企收款账号", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YqzlReceivingAccount yqzlReceivingAccount)
    {
        List<YqzlReceivingAccount> list = yqzlReceivingAccountService.selectYqzlReceivingAccountList(yqzlReceivingAccount);
        ExcelUtil<YqzlReceivingAccount> util = new ExcelUtil<YqzlReceivingAccount>(YqzlReceivingAccount.class);
        util.exportExcel(response, list, "银企收款账号数据");
    }

    /**
     * 获取银企收款账号详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:account:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(yqzlReceivingAccountService.selectYqzlReceivingAccountById(id));
    }

    /**
     * 新增银企收款账号
     */
    //@PreAuthorize("@ss.hasPermi('system:account:add')")
    @Log(title = "银企收款账号", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YqzlReceivingAccount yqzlReceivingAccount)
    {
        return toAjax(yqzlReceivingAccountService.insertYqzlReceivingAccount(yqzlReceivingAccount));
    }

    /**
     * 修改银企收款账号
     */
    //@PreAuthorize("@ss.hasPermi('system:account:edit')")
    @Log(title = "银企收款账号", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YqzlReceivingAccount yqzlReceivingAccount)
    {
        return toAjax(yqzlReceivingAccountService.updateYqzlReceivingAccount(yqzlReceivingAccount));
    }

    /**
     * 删除银企收款账号
     */
    //@PreAuthorize("@ss.hasPermi('system:account:remove')")
    @Log(title = "银企收款账号", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(yqzlReceivingAccountService.deleteYqzlReceivingAccountByIds(ids));
    }
}
