package org.ruoyi.core.oasystem.service.impl;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.mapper.TopNotifyMapper;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.domain.dto.OaProjectFlowUtilDto;
import org.ruoyi.core.oasystem.mapper.*;
import org.ruoyi.core.oasystem.service.IOaProjectFlowMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-06
 */
@Service
public class OaProjectFlowMainServiceImpl implements IOaProjectFlowMainService
{
    @Autowired
    private OaProjectFlowMainMapper oaProjectFlowMainMapper;

    @Autowired
    private OaProjectFlowAssociationMapper oaProjectFlowAssociationMapper;
    @Autowired
    private OaProjectPayerAssociationMapper oaProjectPayerAssociationMapper;

    @Autowired
    private OaEditApproveGeneralityUserMapper oaEditApproveGeneralityUserMapper;

    @Autowired
    private OaEditApproveGeneralityEditRecordsMapper oaEditApproveGeneralityEditRecordsMapper;

    @Autowired
    private TopNotifyMapper topNotifyMapper;

    @Autowired
    private OaEditApproveGeneralityRecordsMapper oaEditApproveGeneralityRecordsMapper;
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public OaProjectFlowMain selectOaProjectFlowMainById(Long id)
    {
        return oaProjectFlowMainMapper.selectOaProjectFlowMainById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaProjectFlowMain 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<OaProjectFlowMain> selectOaProjectFlowMainList(OaProjectFlowMain oaProjectFlowMain)
    {
        return oaProjectFlowMainMapper.selectOaProjectFlowMainList(oaProjectFlowMain);
    }
    public List<Map<String,Object>> selectOaProjectList(OaProjectFlowMain oaProjectFlowMain, String selectType, List<Long> companyIdList, List<Long> filterOaApplyId)
    {
        if (!"0".equals(selectType)) {
            //加入新的查询条件，公司id，过滤后的主表id（主要是涉及其他视图的查询）
            if (filterOaApplyId == null) {
                return new ArrayList<>();
            }
            List<Map<String, Object>> maps = oaProjectFlowMainMapper.selectOaProjectFlowListByFilterOaApplyIdAndCompanyIdList(oaProjectFlowMain, companyIdList, filterOaApplyId);
            List<String> ids = new ArrayList<>();
            for (Map<String, Object> map : maps) {
                ids.add(map.get("id").toString());
            }
            List<String> strings = this.delRepeat1(ids);

            List<Map<String, Object>> maps1 = this.handleReturnList(strings,maps, selectType);

            return maps1;
        }


        //下面的是正常查询所有列表

//        oaProjectFlowMainMapper.getAllData();
        List<Map<String, Object>> maps = oaProjectFlowMainMapper.selectOaProjectFlowList(oaProjectFlowMain);
        List<String> ids = new ArrayList<>();
        for (Map<String, Object> map : maps) {
            ids.add(map.get("id").toString());
        }
        List<String> strings = this.delRepeat1(ids);

        List<Map<String, Object>> maps1 = this.handleReturnList(strings,maps, selectType);

        return maps1;
    }


    //去重
    public static List<String> delRepeat1(List<String> list) {
        List<String> strings = new ArrayList<>(new TreeSet<>(list));
        Collections.reverse(strings);
        return strings;
    }

    public List<Map<String,Object>> handleReturnList(List<String> ids,List<Map<String, Object>> maps, String selectType){
        List<Map<String,Object>> returnList = new ArrayList<>();
        Long currentUserId = SecurityUtils.getLoginUser().getUserId();
        String oaApplyType = "3";
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyType("1");
        topNotify.setOaNotifyType(oaApplyType);
        topNotify.setOaNotifyStep("1");
        TopNotify topNotify1 = new TopNotify();
        topNotify1.setNotifyType("1");
        topNotify1.setOaNotifyType(oaApplyType);
        topNotify1.setOaNotifyStep("2");
        for (String id : ids) {
            HashMap<String, Object>  returnMap = new HashMap<>();
            List<Map<String,Object>> dateList = new ArrayList<>();
            for(int i = 0;i<maps.size();i++){
                if(id.equals(maps.get(i).get("id").toString())){

                        returnMap.put("id",maps.get(i).get("id"));
                        returnMap.put("companyNo",maps.get(i).get("companyNo"));
                        returnMap.put("isLinkageCwxmgl",maps.get(i).get("isLinkageCwxmgl"));
                        returnMap.put("accountingField",maps.get(i).get("accountingField"));
                        returnMap.put("accountingFieldName",maps.get(i).get("accountingFieldName"));
                        returnMap.put("modelName",maps.get(i).get("modelName"));
                        returnMap.put("remark",maps.get(i).get("remark"));
                        dateList.add(maps.get(i));
                    //补充页面缺失数据
                    //负责人
                    List<OaEditApproveGeneralityUser> oaEditApproveGeneralityUsers = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(oaApplyType, Long.parseLong(id));
                    String salesman= oaEditApproveGeneralityUsers.stream().filter(t -> "0".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserNickName).collect(Collectors.joining(","));
                    String financialStaff = oaEditApproveGeneralityUsers.stream().filter(t -> "1".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserNickName).collect(Collectors.joining(","));
                    returnMap.put("salesman", salesman);
                    returnMap.put("financialStaff", financialStaff);
                    //查最新的审核状态 - 未知悉的情况，说明流程未结束
                    PageHelper.clearPage();
                    OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, Long.parseLong(id), "0");
                    if (oaEditApproveGeneralityEditRecords != null) {
                        returnMap.put("checkStatus", oaEditApproveGeneralityEditRecords.getCheckStatus());
                        returnMap.put("rejectFlag", oaEditApproveGeneralityEditRecords.getRejectFlag());
                        returnMap.put("confirmFlag", oaEditApproveGeneralityEditRecords.getConfirmFlag());
                        //修改的判断方法 oa_apply_records_old_id和oa_apply_records_new_id都不为空而且不相等
                        if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() != null && !oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId().equals(oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId())) {
                            returnMap.put("updateType", "1");
                        }
                        //删除的判断方法 oa_apply_records_old_id 不为空 并且oa_apply_records_new_id 为空
                        if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() == null) {
                            returnMap.put("updateType", "2");
                        }
                        if ("1".equals(selectType)) {
                            //查询的是待我审批，找到提交人  --->  也就是找编辑人员的姓名
                            returnMap.put("editUserNickName", oaEditApproveGeneralityEditRecords.getEditUserNickName());
                            //找到提交时间
                            returnMap.put("editTime", oaEditApproveGeneralityEditRecords.getEditTime());
                            //修改说明
                            returnMap.put("editInfo", oaEditApproveGeneralityEditRecords.getEditInfo());
                        } else if ("2".equals(selectType)) {
                            //查询的是我的提交，找到审批人
                            returnMap.put("checkUserNickName", oaEditApproveGeneralityEditRecords.getCheckUserNickName());
                            //找到审核时间
                            returnMap.put("checkTime", oaEditApproveGeneralityEditRecords.getCheckTime());
                        }

                        //查询当前用户的权限问题，是否是有本条记录的审核，是否有本条记录的已知悉操作
                        //审核权限
                        topNotify.setOaApplyId(Long.parseLong(id));
                        List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
                        //知悉权限
                        topNotify1.setOaApplyId(Long.parseLong(id));
                        List<TopNotify> topNotifies1 = topNotifyMapper.selectTopNotifyList1(topNotify1);
                        boolean checkFlag = topNotifies.stream().anyMatch(t -> t.getDisposeUser().equals(currentUserId));
                        boolean confirmFlag = topNotifies1.stream().anyMatch(t -> t.getDisposeUser().equals(currentUserId));
                        if (checkFlag) {
                            returnMap.put("showCheckFlag", "1");
                        } else {
                            returnMap.put("showCheckFlag", "0");
                        }
                        if (confirmFlag) {
                            returnMap.put("showConfirmFlag", "1");
                        } else {
                            returnMap.put("showConfirmFlag", "0");
                        }
                    } else {
                        //因为map跟一般对象的不一样，所以把上面没有的都重新赋值一遍空
                        returnMap.put("checkStatus", null);
                        returnMap.put("rejectFlag", null);
                        //没有找到审核中的话，就给1-已知悉
                        returnMap.put("confirmFlag", "1");
                        returnMap.put("updateType", null);
                        returnMap.put("editUserNickName", null);
                        returnMap.put("editTime", null);
                        returnMap.put("editInfo", null);
                        returnMap.put("checkUserNickName", null);
                        returnMap.put("checkTime", null);
                        returnMap.put("showCheckFlag", "0");
                        returnMap.put("showConfirmFlag", "0");
                    }
                }
                }


            returnMap.put("dataList",dateList);

            returnList.add(returnMap);
        }

        return returnList;

    }
    /**
     * 新增【请填写功能名称】
     * 
     * @param oaProjectFlowMain 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertOaProjectFlowMain(OaProjectFlowMain oaProjectFlowMain)
    {
        oaProjectFlowMain.setCreateTime(DateUtils.getNowDate());
        return oaProjectFlowMainMapper.insertOaProjectFlowMain(oaProjectFlowMain);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaProjectFlowMain 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateOaProjectFlowMain(OaProjectFlowMain oaProjectFlowMain)
    {
        oaProjectFlowMain.setUpdateTime(DateUtils.getNowDate());
        return oaProjectFlowMainMapper.updateOaProjectFlowMain(oaProjectFlowMain);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaProjectFlowMainByIds(Long[] ids)
    {
        return oaProjectFlowMainMapper.deleteOaProjectFlowMainByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteOaProjectFlowMainById(Long id)
    {
        String oaApplyType = "3";
        int ii = topNotifyMapper.deleteTopNotifyByOaNotifyTypeAndOaApplyId(oaApplyType, id);
        int ii1 = oaEditApproveGeneralityEditRecordsMapper.deleteOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, id);
        int ii2 = oaEditApproveGeneralityRecordsMapper.deleteOaEditApproveGeneralityRecordsByOaApplyTypeAndOaApplyId(oaApplyType, id);
        int ii3 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(oaApplyType, id);
        int i = oaProjectFlowMainMapper.deleteOaProjectFlowMainById(id);
        int a = 0;
        int b = 0;
        //先通过主表找association表所涉及的所有
        List<Long> pfaIds = oaProjectFlowAssociationMapper.selectOaProjectFlowIdsAssociationByMainId(id);
        if (i > 0) {
            a = oaProjectFlowAssociationMapper.deleteByMainId(id);
        }
        if (a > 0) {
            b = oaProjectPayerAssociationMapper.deleteByProAssId(pfaIds);
        }
        return i;
    }

    @Override
    public int addProjectAndFlow(OaProjectFlowUtil oaProjectFlowUtil, LoginUser loginUser) {
        if(null == oaProjectFlowUtil.getId()|| oaProjectFlowUtil.getId().equals("")){
            this.addProMainData(oaProjectFlowUtil,loginUser);
        }else {
            //添加时因为不确定有没有删除原本的数据，所以直接删除原本所有的数据后进行添加
            //删除主表
            oaProjectFlowMainMapper.deleteOaProjectFlowMainById(Long.valueOf(oaProjectFlowUtil.getId()));

            //删除关联表先查询所有关联的id
            List<Map<String, Object>> projectByMainId = oaProjectFlowAssociationMapper.getProjectByMainId(Long.valueOf(oaProjectFlowUtil.getId()));
            List<Long> prolist = new ArrayList<>();
            for (Map<String, Object> map : projectByMainId) {
                prolist.add(Long.valueOf(map.get("id").toString()));
            }
            oaProjectFlowAssociationMapper.deleteByMainId(Long.valueOf(oaProjectFlowUtil.getId()));
            //删除关联的收付款人
            if(prolist.size() > 0 && null != prolist){
                    oaProjectPayerAssociationMapper.deleteByProAssId(prolist);
            }
            //添加新的数据
            this.addProMainData(oaProjectFlowUtil,loginUser);
        }

        return 1;
    }

    public int addProMainData(OaProjectFlowUtil oaProjectFlowUtil, LoginUser loginUser){
        // 先添加主表
        OaProjectFlowMain oaProjectFlowMain = new OaProjectFlowMain();
        oaProjectFlowMain.setCompanyNo(oaProjectFlowUtil.getCompanyNo());
        oaProjectFlowMain.setModelId(Long.valueOf(oaProjectFlowUtil.getModelId()));
        oaProjectFlowMain.setModelName(oaProjectFlowUtil.getModelName());
        oaProjectFlowMain.setIsLinkageCwxmgl(oaProjectFlowUtil.getIsLinkageCwxmgl());
        oaProjectFlowMain.setAccountingField(oaProjectFlowUtil.getAccountingField());
        oaProjectFlowMain.setAccountingFieldName(oaProjectFlowUtil.getAccountingFieldName());
        oaProjectFlowMain.setProjectTypeField(oaProjectFlowUtil.getProjectTypeField());
        oaProjectFlowMain.setProjectTypeFieldName(oaProjectFlowUtil.getProjectTypeFieldName());

        oaProjectFlowMain.setFeeCompanyField(oaProjectFlowUtil.getFeeCompanyField());
        oaProjectFlowMain.setFeeCompanyFieldName(oaProjectFlowUtil.getFeeCompanyFieldName());

        oaProjectFlowMain.setProjectField(oaProjectFlowUtil.getProjectField());
        oaProjectFlowMain.setRemark(oaProjectFlowUtil.getRemark());
        oaProjectFlowMain.setCreateTime(DateUtils.getNowDate());
        oaProjectFlowMain.setStatus("0");
        oaProjectFlowMain.setCreateBr(loginUser.getUsername());
        int i = oaProjectFlowMainMapper.insertOaProjectFlowMain(oaProjectFlowMain);

        Long mainId = oaProjectFlowMain.getId();
        List<OaProjectFlowUtil2> proList = oaProjectFlowUtil.getProList();
        // 得到id后添加关联项目
        for (OaProjectFlowUtil2 oaProjectFlowUtil2 : proList) {
            OaProjectFlowAssociation oaProjectFlowAssociation = new OaProjectFlowAssociation();
            oaProjectFlowAssociation.setProjectFlowMainId(mainId);
            oaProjectFlowAssociation.setProjectId(Long.valueOf(oaProjectFlowUtil2.getProjectId()));
            oaProjectFlowAssociation.setProjectName(oaProjectFlowUtil2.getProjectName());
            oaProjectFlowAssociation.setStatus("0");
            oaProjectFlowAssociation.setCreateBr(loginUser.getUsername());
            oaProjectFlowAssociation.setCreateTime(DateUtils.getNowDate());
            oaProjectFlowAssociationMapper.insertOaProjectFlowAssociation(oaProjectFlowAssociation);
            Long projectid = oaProjectFlowAssociation.getId();
            List<OaProjectPayerAssociation> tableList = oaProjectFlowUtil2.getTableList();
            for (OaProjectPayerAssociation oaProjectPayerAssociation : tableList) {
                oaProjectPayerAssociation.setPfaId(projectid);
                oaProjectPayerAssociation.setStatus("0");
                oaProjectPayerAssociation.setCreateBr(loginUser.getUsername());
                oaProjectFlowAssociation.setCreateTime(DateUtils.getNowDate());
                oaProjectPayerAssociationMapper.insertOaProjectPayerAssociation(oaProjectPayerAssociation);
            }
        }
        return i;
    }

    @Override
    public Map<String, Object> getOaProjectFlowMainById(Long id) {
        HashMap<String, Object> returnMap = new HashMap<>();
        OaProjectFlowMain oaProjectFlowMain = new OaProjectFlowMain();
        oaProjectFlowMain.setId(id);
        List<Map<String, Object>> maps = oaProjectFlowMainMapper.selectOaProjectFlowList(oaProjectFlowMain);

        Map<String, Object> map = maps.get(0);
        returnMap.put("projectList",maps);
        returnMap.put("projectdataMap",map);
      return returnMap;
    }

    @Override
    public Map<String, Object> getupDataById(Long id) {
        HashMap<String, Object> returnMap = new HashMap<>();
        OaProjectFlowMain oaProjectFlowMain = oaProjectFlowMainMapper.selectOaProjectFlowMainById(id);
        returnMap.put("id",id);
        returnMap.put("companyNo",Integer.parseInt(oaProjectFlowMain.getCompanyNo()) );
        returnMap.put("modelId",oaProjectFlowMain.getModelId());
        returnMap.put("isLinkageCwxmgl",oaProjectFlowMain.getIsLinkageCwxmgl());
        returnMap.put("accountingField",oaProjectFlowMain.getAccountingField());
        returnMap.put("accountingFieldName",oaProjectFlowMain.getAccountingFieldName());


        returnMap.put("projectTypeField",oaProjectFlowMain.getProjectTypeField());
        returnMap.put("projectTypeFieldName",oaProjectFlowMain.getProjectTypeFieldName());
        returnMap.put("feeCompanyField",oaProjectFlowMain.getFeeCompanyField());
        returnMap.put("feeCompanyFieldName",oaProjectFlowMain.getFeeCompanyFieldName());

        returnMap.put("projectField",oaProjectFlowMain.getProjectField());
        returnMap.put("modelName",oaProjectFlowMain.getModelName());
        returnMap.put("remark",oaProjectFlowMain.getRemark());
        //查询项目关联表数据
       List<Map<String,Object>> projectList = oaProjectFlowAssociationMapper.getProjectByMainId(id);
        for (Map<String, Object> map : projectList) {
            //查询关联的联系人数据
            List<Map<String,Object>> properList = oaProjectPayerAssociationMapper.selectDataByProId(map.get("id").toString());
            map.put("tableList",properList);
        }

        returnMap.put("proList",projectList);
        //组装财务责任人和业务责任人
        List<OaEditApproveGeneralityUser> oaEditApproveGeneralityUsers = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId("3", id);
        List<Long> salesmanList = oaEditApproveGeneralityUsers.stream().filter(t -> "0".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserId).collect(Collectors.toList());
        List<Long> financialStaffList = oaEditApproveGeneralityUsers.stream().filter(t -> "1".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserId).collect(Collectors.toList());
        returnMap.put("salesmanList", salesmanList);
        returnMap.put("financialStaffList", financialStaffList);
        //组装修改前JSON对应的id
        //找最新的、已经知悉的审批记录表（未知悉进不来修改页面，放心处理）
        PageHelper.clearPage();
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId("3", id, "1");
        //查出来的最新的记录表修改后id是要修改的之前的id
        if (oaEditApproveGeneralityEditRecords != null) {
            returnMap.put("oaApplyRecordsOldId", oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId());
        } else {
            returnMap.put("oaApplyRecordsOldId", null);
        }
        return returnMap;
    }

    @Override
    public Long getTotal(OaProjectFlowMain oaProjectFlowMain) {
        return oaProjectFlowMainMapper.getTotal(oaProjectFlowMain);
    }

    /**
     * 根据条件查询
     * @param oaProjectFlowMain
     * @return {@link List}<{@link OaProjectFlowUtil}>
     */
    @Override
    public List<OaProjectFlowUtil> getDataByParams(OaProjectFlowMain oaProjectFlowMain) {
        List<OaProjectFlowUtil> returnList = new ArrayList<>();
        List<OaProjectFlowMain> oaProjectFlowMains = oaProjectFlowMainMapper.selectOaProjectFlowMainList(oaProjectFlowMain);
        for (OaProjectFlowMain projectFlowMain : oaProjectFlowMains) {
            OaProjectFlowUtil oaProjectFlowUtil = new OaProjectFlowUtil();
            oaProjectFlowUtil.setId(projectFlowMain.getId().toString());
            oaProjectFlowUtil.setCompony(projectFlowMain.getCompanyNo());
            oaProjectFlowUtil.setModelId(projectFlowMain.getModelId().toString());
            oaProjectFlowUtil.setModelName(projectFlowMain.getModelName());
            oaProjectFlowUtil.setIsLinkageCwxmgl(projectFlowMain.getIsLinkageCwxmgl());
            oaProjectFlowUtil.setAccountingField(projectFlowMain.getAccountingField());

            List<OaProjectFlowUtil2> proList = new ArrayList<>();
            OaProjectFlowAssociation oaProjectFlowAssociation = new OaProjectFlowAssociation();
            oaProjectFlowAssociation.setProjectFlowMainId(oaProjectFlowMain.getId());
            List<OaProjectFlowAssociation> oaProjectFlowAssociations = oaProjectFlowAssociationMapper.selectOaProjectFlowAssociationList(oaProjectFlowAssociation);
            for (OaProjectFlowAssociation projectFlowAssociation : oaProjectFlowAssociations) {
                OaProjectFlowUtil2 oaProjectFlowUtil2 = new OaProjectFlowUtil2();
                oaProjectFlowUtil2.setId(projectFlowAssociation.getId());
                oaProjectFlowUtil2.setProjectId(projectFlowAssociation.getProjectId().toString());
                oaProjectFlowUtil2.setProjectName(projectFlowAssociation.getProjectName());

                List<OaProjectPayerAssociation> oaProjectPayerAssociations = oaProjectPayerAssociationMapper.selectListByProId(projectFlowAssociation.getId().toString());
                oaProjectFlowUtil2.setTableList(oaProjectPayerAssociations);
                proList.add(oaProjectFlowUtil2);
            }

            oaProjectFlowUtil.setProList(proList);

            returnList.add(oaProjectFlowUtil);

        }
        return returnList;
    }

    @Override
    public Long getTotalByCompanyIdList(OaProjectFlowMain oaProjectFlowMain, List<Long> companyIdList) {
        return oaProjectFlowMainMapper.getTotalByCompanyIdList(oaProjectFlowMain, companyIdList);
    }

    @Override
    public Long getTotalByCompanyIdListAndFilterOaApplyId(OaProjectFlowMain oaProjectFlowMain, List<Long> companyIdList, List<Long> filterOaApplyId) {
        return oaProjectFlowMainMapper.getTotalByCompanyIdListAndFilterOaApplyId(oaProjectFlowMain, companyIdList, filterOaApplyId);
    }



    //原来插入的方法有问题，需要稍微改造一下
    public int addProjectAndFlowDto(OaProjectFlowUtilDto oaProjectFlowUtilDto, LoginUser loginUser) {
        if(null == oaProjectFlowUtilDto.getId()){
            this.addProMainDataDto(oaProjectFlowUtilDto,loginUser);
        }else {
            //添加时因为不确定有没有删除原本的数据，所以直接删除原本所有的数据后进行添加
            //删除主表
            oaProjectFlowMainMapper.deleteOaProjectFlowMainById(oaProjectFlowUtilDto.getId());

            //删除关联表先查询所有关联的id
            List<Map<String, Object>> projectByMainId = oaProjectFlowAssociationMapper.getProjectByMainId(oaProjectFlowUtilDto.getId());
            List<Long> prolist = new ArrayList<>();
            for (Map<String, Object> map : projectByMainId) {
                prolist.add(Long.valueOf(map.get("id").toString()));
            }
            oaProjectFlowAssociationMapper.deleteByMainId(oaProjectFlowUtilDto.getId());
            //删除关联的收付款人
            oaProjectPayerAssociationMapper.deleteByProAssId(prolist);
            //添加新的数据
            this.addProMainDataDto(oaProjectFlowUtilDto,loginUser);
        }

        return 1;
    }

    public int addProMainDataDto(OaProjectFlowUtilDto oaProjectFlowUtilDto, LoginUser loginUser){
        // 先添加主表
        OaProjectFlowMain oaProjectFlowMain = new OaProjectFlowMain();
        oaProjectFlowMain.setId(oaProjectFlowUtilDto.getId());
        oaProjectFlowMain.setCompanyNo(oaProjectFlowUtilDto.getCompanyNo());
        oaProjectFlowMain.setModelId(oaProjectFlowUtilDto.getModelId());
        oaProjectFlowMain.setModelName(oaProjectFlowUtilDto.getModelName());
        oaProjectFlowMain.setAccountingField(oaProjectFlowUtilDto.getAccountingField());
        oaProjectFlowMain.setAccountingFieldName(oaProjectFlowUtilDto.getAccountingFieldName());
        oaProjectFlowMain.setProjectTypeField(oaProjectFlowUtilDto.getProjectTypeField());
        oaProjectFlowMain.setProjectTypeFieldName(oaProjectFlowUtilDto.getProjectTypeFieldName());
        oaProjectFlowMain.setProjectField(oaProjectFlowUtilDto.getProjectField());




        oaProjectFlowMain.setRemark(oaProjectFlowUtilDto.getRemark());
        oaProjectFlowMain.setCreateTime(DateUtils.getNowDate());
        oaProjectFlowMain.setStatus(oaProjectFlowUtilDto.getStatus());
        oaProjectFlowMain.setCreateBr(loginUser.getUsername());


        int i = oaProjectFlowMainMapper.insertOaProjectFlowMain(oaProjectFlowMain);

        Long mainId = oaProjectFlowMain.getId();
        List<OaProjectFlowUtil2> proList = oaProjectFlowUtilDto.getProList();
        // 得到id后添加关联项目
        for (OaProjectFlowUtil2 oaProjectFlowUtil2 : proList) {
            OaProjectFlowAssociation oaProjectFlowAssociation = new OaProjectFlowAssociation();
            oaProjectFlowAssociation.setProjectFlowMainId(mainId);
            oaProjectFlowAssociation.setProjectId(Long.valueOf(oaProjectFlowUtil2.getProjectId()));
            oaProjectFlowAssociation.setProjectName(oaProjectFlowUtil2.getProjectName());
            oaProjectFlowAssociation.setStatus("0");
            oaProjectFlowAssociation.setCreateBr(loginUser.getUsername());
            oaProjectFlowAssociation.setCreateTime(DateUtils.getNowDate());
            oaProjectFlowAssociationMapper.insertOaProjectFlowAssociation(oaProjectFlowAssociation);
            Long projectid = oaProjectFlowAssociation.getId();
            List<OaProjectPayerAssociation> tableList = oaProjectFlowUtil2.getTableList();
            for (OaProjectPayerAssociation oaProjectPayerAssociation : tableList) {
                oaProjectPayerAssociation.setPfaId(projectid);
                oaProjectPayerAssociation.setStatus("0");
                oaProjectPayerAssociation.setCreateBr(loginUser.getUsername());
                oaProjectFlowAssociation.setCreateTime(DateUtils.getNowDate());
                oaProjectPayerAssociationMapper.insertOaProjectPayerAssociation(oaProjectPayerAssociation);
            }
        }
        return i;
    }
}
